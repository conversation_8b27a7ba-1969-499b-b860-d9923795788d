name: Baseline Image Build

on:
  pull_request:
    paths:
      - 'baseline/**'
      - '.github/workflows/baseline-image.yml'

permissions:
  id-token: write
  contents: read
  pull-requests: read

jobs:
  build-images:
    runs-on: ubuntu-latest-m
    strategy:
      matrix:
        variant: [cpu, gpu]
      fail-fast: false

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up image tags
        id: tags
        run: |
          VERSION=$(grep -m1 "version = " baseline/images/pyproject.toml | cut -d'"' -f2)
          PR_NUMBER=$(echo $GITHUB_REF | sed 's:refs/pull/::' | sed 's:/merge::')
          TAG="${VERSION}-pr${PR_NUMBER}-${GITHUB_SHA::7}"
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "tag=${TAG}" >> $GITHUB_OUTPUT
          echo "Generated application tag: $TAG"

      - name: Generate cache keys
        id: cache-keys
        run: |
          # Use uv.lock content hash for precise dependency caching
          UV_LOCK_HASH=$(sha256sum baseline/images/uv.lock | cut -d' ' -f1)
          echo "uv-lock-hash=${UV_LOCK_HASH}" >> $GITHUB_OUTPUT
          echo "  UV Lock Hash: ${UV_LOCK_HASH}"
          echo "  Using LOCAL cache (faster for large images)"
          echo "  Cache invalidates only when uv.lock changes"

      - name: Get CodeArtifact token
        id: codeartifact-token
        uses: gametimesf/github-actions-python/actions/codeartifact-token@main
        with:
          role-arn: arn:aws:iam::728489771660:role/internal/github/mlplatform.build
          region: us-west-2
          codeartifact-domain: ml-artifacts
          codeartifact-domain-owner: 728489771660

      - name: Set up CodeArtifact authentication
        uses: gametimesf/github-actions-python/actions/codeartifact-auth@main
        with:
          token: ${{ steps.codeartifact-token.outputs.token }}
          repository-endpoint: ${{ steps.codeartifact-token.outputs.repository-endpoint }}
          codeartifact-domain: ml-artifacts
          codeartifact-domain-owner: 728489771660
          region: us-west-2

      - name: Configure AWS credentials for ECR
        uses: gametimesf/github-actions/actions/auth-to-aws@v0
        with:
          role-arn: arn:aws:iam::728489771660:role/internal/github/mlplatform.build
          region: us-west-1

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr
        with:
          mask-password: true

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-flags: --debug

      # local cache with uv.lock-based keys
      - name: Cache Docker layers with uv.lock keys
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ matrix.variant }}-deps-${{ steps.cache-keys.outputs.uv-lock-hash }}
          restore-keys: |
            ${{ runner.os }}-buildx-${{ matrix.variant }}-deps-

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          # disabling provenance stops empty image builds: https://github.com/docker/build-push-action/issues/840
          provenance: false
          context: baseline/images
          push: true
          secrets: |
            CODEARTIFACT_AUTH_TOKEN=${{ steps.codeartifact-token.outputs.token }}
          tags: ${{ steps.login-ecr.outputs.registry }}/baseline-production:${{ steps.tags.outputs.tag }}-${{ matrix.variant }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max
          build-args: |
            BASE_IMAGE=${{ matrix.variant == 'gpu' && 'public.ecr.aws/sagemaker/sagemaker-distribution:2.3.1-gpu' || 'public.ecr.aws/sagemaker/sagemaker-distribution:2.3.1-cpu' }}

      - name: Replace existing cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

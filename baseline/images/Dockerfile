ARG BASE_IMAGE="public.ecr.aws/sagemaker/sagemaker-distribution:2.3.1-cpu"
FROM ${BASE_IMAGE}

ENV HOME=/home/<USER>
    UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy

# Copy uv from the official distroless image
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

COPY uv.lock ./
COPY pyproject.toml ./

# Install packages directly into conda environment using frozen lock file
# uid and gid are set to match the sagemaker-user user in the base image
RUN --mount=type=secret,id=CODEARTIFACT_AUTH_TOKEN,uid=1000,gid=100 \
    --mount=type=cache,target=/root/.cache/uv,sharing=locked \
    # Set up UV environment for CodeArtifact
    export UV_INDEX_CODEARTIFACT_URL="https://ml-artifacts-************.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" && \
    export UV_INDEX_CODEARTIFACT_USERNAME=aws && \
    export UV_INDEX_CODEARTIFACT_PASSWORD=$(cat /run/secrets/CODEARTIFACT_AUTH_TOKEN) && \
    # Convert lock file to requirements format with exact versions (--frozen)
    uv export --format requirements-txt --no-dev --frozen > requirements.txt && \
    # Install packages into conda base environment using micromamba
    micromamba run -n base uv pip install -r requirements.txt && \
    # Clean up
    rm pyproject.toml requirements.txt uv.lock

ENTRYPOINT [ "entrypoint-jupyter-server" ]
CMD []

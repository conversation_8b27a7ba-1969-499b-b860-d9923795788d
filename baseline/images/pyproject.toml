[build-system]
build-backend = "hatchling.build"
requires = ["hatchling"]

[project]
authors = [{name = "ML Platform"}]
dependencies = [
  "baseline-sdk>=1.0.0",
  "boto3>=1.36.17",
  "catboost>=1.2.5",
  "grpcio>=1.70.0",
  "ipykernel>=6.29.5",
  "ipywidgets>=8.1.5",
  "jupyterlab>=4.3.5",
  "mlutils[aiohttp,grpc,pandas,redis,sklearn]>=1.11.0",
  "numpy>=2.2.2",
  "pandas~=2.2.3",
  "papermill>=2.6.0",
  "polars~=1.22.0",
  "pyarrow~=19.0.0",
  "scikit-learn>=1.6.1",
  "scipy>=1.15.1",
  "seaborn>=0.13.2",
  "snowflake-connector-python[pandas]>=3.13.2",
  "snowflake-sqlalchemy>=1.7.3",
  "sqlalchemy>=2.0.38",
  "xgboost~=2.1.0"
]
description = "Custom Baseline Image"
name = "baseline-image"
requires-python = ">=3.10,<3.12"
version = "1.4.0"

[project.optional-dependencies]
test = [
  "docker>=7.1.0",
  "pytest>=8.3.4",
  "requests>=2.28.0",
  "websocket-client>=1.8.0"
]

[tool.hatch.build.targets.wheel]
include = ["Dockerfile", "README.md", "build.sh"]

[tool.hatch.metadata]
allow-direct-references = true

[tool.semantic_release]
build_command = ""
changelog_file = "CHANGELOG.md"
commit_parser = "conventional"
tag_format = "baseline-images-v{version}"
upload_to_release = true
upload_to_repository = false
version_toml = ["pyproject.toml:project.version"]

[tool.semantic_release.branches]
main.match = "main"

[[tool.uv.index]]
name = "codeartifact"
url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/"

[tool.uv.sources]
baseline-sdk = {index = "codeartifact"}
mlutils = {index = "codeartifact"}

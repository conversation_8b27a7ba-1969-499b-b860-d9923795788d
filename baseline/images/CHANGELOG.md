# CHANGELOG


## v1.4.0 (2025-05-29)

### Bug Fixes

- Correct FamilyPipelineActivities init, refactor test config
  ([#141](https://github.com/gametimesf/mlplatform/pull/141),
  [`43b02ce`](https://github.com/gametimesf/mlplatform/commit/43b02cecdbf06acd9f6bc0afaf10eb7b1b607b31))

### Chores

- Cut new baseline image ([#137](https://github.com/gametimesf/mlplatform/pull/137),
  [`b0aab03`](https://github.com/gametimesf/mlplatform/commit/b0aab031c9249d7ba524cea98c385d8868fd2db9))

- Flexible gametime-protos for baseline service image
  ([#156](https://github.com/gametimesf/mlplatform/pull/156),
  [`7200414`](https://github.com/gametimesf/mlplatform/commit/7200414e0e4e4602170398c4bb66f01f5095e013))

chore: flexible gametime-protos for baseline service image

- Remove ssh key from ci ([#145](https://github.com/gametimesf/mlplatform/pull/145),
  [`023d5c3`](https://github.com/gametimesf/mlplatform/commit/023d5c30678984b09405255633fad4e8f413bb35))

- Revoke alana and joseph lin baseline access
  ([#155](https://github.com/gametimesf/mlplatform/pull/155),
  [`066f333`](https://github.com/gametimesf/mlplatform/commit/066f333d252f3adbe296eccb51dec0924619f92b))

### Features

- Add redis dep ([#158](https://github.com/gametimesf/mlplatform/pull/158),
  [`4776e11`](https://github.com/gametimesf/mlplatform/commit/4776e110de9aa0147c88a0d1c69b0e010992ed3a))

feat: add redis dep

- Baseline image to uv lock migration ([#157](https://github.com/gametimesf/mlplatform/pull/157),
  [`6ab653b`](https://github.com/gametimesf/mlplatform/commit/6ab653bdb1336add8387b1f7f9ba2f642fe7c4d1))

- Correct caching baseline sagemaker ([#148](https://github.com/gametimesf/mlplatform/pull/148),
  [`5175c82`](https://github.com/gametimesf/mlplatform/commit/5175c82a04f9226661fc3f35621a8f9f48dd4e47))

- Ensure split extractor is always string
  ([#150](https://github.com/gametimesf/mlplatform/pull/150),
  [`1f2058e`](https://github.com/gametimesf/mlplatform/commit/1f2058ec127771a4590a9b2d91320f37bf92678e))

* feat: ensure split extractor is always string

* feat: remove unneeded import

* docs: update function docs

* feat: differentiate between missing types

* fix: pre commit

- Implement draft mode restriction for online; refactor activities
  ([#151](https://github.com/gametimesf/mlplatform/pull/151),
  [`8ffcb65`](https://github.com/gametimesf/mlplatform/commit/8ffcb658455756b3007e266063eada86f6a5ba62))

- Implement incremental processing loop in FamilyPipeline workflow
  ([#138](https://github.com/gametimesf/mlplatform/pull/138),
  [`9229c4c`](https://github.com/gametimesf/mlplatform/commit/9229c4c224556a6cb3ddabd2d5f151e9faed4532))

- Implement Temporal schedules for incremental feature processing
  ([#139](https://github.com/gametimesf/mlplatform/pull/139),
  [`cf67342`](https://github.com/gametimesf/mlplatform/commit/cf67342ae2d5024709088d5b37b41cd5621dacf0))

- Migrate baseline service to uv sync ([#154](https://github.com/gametimesf/mlplatform/pull/154),
  [`2c678f3`](https://github.com/gametimesf/mlplatform/commit/2c678f36fa5b29237a32aeb3c65bc588d88a206a))

feat: migrate baseline service to uv sync

- Update mlutils package
  ([`be4d74c`](https://github.com/gametimesf/mlplatform/commit/be4d74c7c3c983e3aa0307a9547ecb33997da2c2))

- Update package
  ([`0d6aa81`](https://github.com/gametimesf/mlplatform/commit/0d6aa81b13eff1fe6875fc6857a2d563cbc60c9c))

### Refactoring

- Adapt workflow tests, improve schedule logic
  ([#140](https://github.com/gametimesf/mlplatform/pull/140),
  [`703b3d7`](https://github.com/gametimesf/mlplatform/commit/703b3d7f8555289bc31d535146eaaa3cd50aa6a4))


## v1.3.0 (2025-05-08)

### Bug Fixes

- Update mlutils grpc ([#90](https://github.com/gametimesf/mlplatform/pull/90),
  [`46f6d15`](https://github.com/gametimesf/mlplatform/commit/46f6d155366ec9176d3a1b1571f64a6ff90d089e))

### Chores

- Bump mlutils to 1.9.0 ([#135](https://github.com/gametimesf/mlplatform/pull/135),
  [`35b24bb`](https://github.com/gametimesf/mlplatform/commit/35b24bb29683bffa09dfc882ce8f5aa54ddc6c83))

- Fix precommit checks
  ([`27ade7b`](https://github.com/gametimesf/mlplatform/commit/27ade7b5387e83538f79917a417ad2f68480e8db))

### Continuous Integration

- Automate baseline sdk unit tests ([#89](https://github.com/gametimesf/mlplatform/pull/89),
  [`037a411`](https://github.com/gametimesf/mlplatform/commit/037a4116624c7e0bbe9154b04c82482302de8536))

- Automate mlutils unit tests ([#88](https://github.com/gametimesf/mlplatform/pull/88),
  [`27bc098`](https://github.com/gametimesf/mlplatform/commit/27bc098e34890ea805c95bef6a314eed3381bc52))

### Features

- Add back missing test ([#112](https://github.com/gametimesf/mlplatform/pull/112),
  [`88c0fb7`](https://github.com/gametimesf/mlplatform/commit/88c0fb7737c8b85b9b0ad0005d60a3e3222ecc45))

- Add cost allocation tags to baseline services
  ([#115](https://github.com/gametimesf/mlplatform/pull/115),
  [`3bb92fc`](https://github.com/gametimesf/mlplatform/commit/3bb92fc4445c80a84183287a911161ba539b8dbe))

* feat: add cost allocation tags to baseline services

- Add Job Logs Feature ([#105](https://github.com/gametimesf/mlplatform/pull/105),
  [`b64873c`](https://github.com/gametimesf/mlplatform/commit/b64873c5d600c20f1f32f4f16674a6c1d6ea0948))

- Add unary-stream support to gRPC client interceptors
  ([#102](https://github.com/gametimesf/mlplatform/pull/102),
  [`70d442d`](https://github.com/gametimesf/mlplatform/commit/70d442d09d44885c286508e7c667d70ba98ef148))

- Bump mlctl to 1.3.0
  ([`f06bd49`](https://github.com/gametimesf/mlplatform/commit/f06bd49d3086280c6ed1b8df81ebf7f449859bd1))

- Bump mlplatform to 1.2.0
  ([`39f0c98`](https://github.com/gametimesf/mlplatform/commit/39f0c981364ee6428bf0305c44d8167f61340870))

- Capture all notebook output in mlctl job logs
  ([#110](https://github.com/gametimesf/mlplatform/pull/110),
  [`a0f6d71`](https://github.com/gametimesf/mlplatform/commit/a0f6d71ed8bbbab76b76d2ccffdfe5af68fd292d))

- Checkpoint initial prism local development
  ([#94](https://github.com/gametimesf/mlplatform/pull/94),
  [`64e7a6e`](https://github.com/gametimesf/mlplatform/commit/64e7a6e0498254e1860a2f74b228c7e0a4c41ff8))

* feat: initial commit of prism service

* feat: add docker compose, get service running locally

* feat: get feature family creation example working

* fix: address PR comments

* fix: pre-commit issues

- Correct pyproject uv tests ref ([#96](https://github.com/gametimesf/mlplatform/pull/96),
  [`d942ea8`](https://github.com/gametimesf/mlplatform/commit/d942ea8ddb73f94a7cbe4a5b180f8e9376a650d5))

- Cross account code artifact access ([#86](https://github.com/gametimesf/mlplatform/pull/86),
  [`63944fe`](https://github.com/gametimesf/mlplatform/commit/63944feebeb78bd207d95b3ca1a3c46b6cd8cd06))

- Efs mount utils to baseline image ([#101](https://github.com/gametimesf/mlplatform/pull/101),
  [`dc7f49a`](https://github.com/gametimesf/mlplatform/commit/dc7f49a911a0972ff364ffe360162ea8ecb24344))

- Enable reading offline spine data from S3
  ([#133](https://github.com/gametimesf/mlplatform/pull/133),
  [`152cee4`](https://github.com/gametimesf/mlplatform/commit/152cee44fe70fb693725d99cffa08bdf025529d1))

* feat: enable reading offline spine data from S3

* fix: address PR comments

- Flexible Snowflake Connection URL Transformer
  ([#108](https://github.com/gametimesf/mlplatform/pull/108),
  [`2a0a9c9`](https://github.com/gametimesf/mlplatform/commit/2a0a9c9292a043abe1a8dc497d7a38f2b8a05c27))

- Force baseline sdk release ([#124](https://github.com/gametimesf/mlplatform/pull/124),
  [`90fad75`](https://github.com/gametimesf/mlplatform/commit/90fad75839a88d272ff0da4159e9347695e09860))

- Implement and test OfflineFeaturesServicer CreateDataset endpoint
  ([#107](https://github.com/gametimesf/mlplatform/pull/107),
  [`c7ac291`](https://github.com/gametimesf/mlplatform/commit/c7ac291dc443ac5b55cd12defbd0e6a89a78be8c))

* feat: Implement and test OfflineFeaturesServicer CreateDataset endpoint

* fix: address PR comments

* fix: get tests working again

- Implement backfill activity logic ([#99](https://github.com/gametimesf/mlplatform/pull/99),
  [`a8ec1be`](https://github.com/gametimesf/mlplatform/commit/a8ec1be925857da36a495e309f0fb70c789cf66c))

- Implement ongoing incremental batch processing workflow
  ([#126](https://github.com/gametimesf/mlplatform/pull/126),
  [`f957912`](https://github.com/gametimesf/mlplatform/commit/f957912802a4fd6b8a25d88a9bbfee3cfc6ce722))

* feat: implement ongoing incremental batch processing workflow

* fix: address PR comments

- Implement redis to s3 flush activity ([#118](https://github.com/gametimesf/mlplatform/pull/118),
  [`d4cc4b8`](https://github.com/gametimesf/mlplatform/commit/d4cc4b80c19f01e984ff104aad58e216ee67fa57))

- Implement S3 fallback for online feature fetching
  ([#122](https://github.com/gametimesf/mlplatform/pull/122),
  [`54d80ee`](https://github.com/gametimesf/mlplatform/commit/54d80eed5618b8559ec8e4c40f2151797eeaa2d6))

- Improve async RPC auth handling in require_permission decorator
  ([#104](https://github.com/gametimesf/mlplatform/pull/104),
  [`93f77ca`](https://github.com/gametimesf/mlplatform/commit/93f77caf9bef2000766ce1e52283876c4eb755cb))

The require_permission decorator now properly handles both unary and streaming RPCs by checking for
  the async generator protocol instead of awaiting all results. This fixes streaming RPC
  authentication while maintaining existing unary RPC behavior.

- Improve equality checker performance and return float
  ([#120](https://github.com/gametimesf/mlplatform/pull/120),
  [`d0ac170`](https://github.com/gametimesf/mlplatform/commit/d0ac170988d7bab88c6c48ad91d47c56427dde15))

* feat: Improve equality checker performance and return float

Was causing some issues when comparing null values - resulted in object datatype

* fix: Update mlutils/mlutils/sklearn/transformers/equality_checker.py

Co-authored-by: Copilot <<EMAIL>>

* fix: precommit formatting

* fix: remove whitespace

---------

- Offboard renee and neil ([#91](https://github.com/gametimesf/mlplatform/pull/91),
  [`386c7b2`](https://github.com/gametimesf/mlplatform/commit/386c7b2a85962e675e617beeaa4c32c9c310f529))

- Point to new protos ([#123](https://github.com/gametimesf/mlplatform/pull/123),
  [`21a46c6`](https://github.com/gametimesf/mlplatform/commit/21a46c6e39b2d7fc66e67b055735f067ea371335))

- Private curation cross account kms access
  ([#125](https://github.com/gametimesf/mlplatform/pull/125),
  [`64ddbb1`](https://github.com/gametimesf/mlplatform/commit/64ddbb127e331360109c39c25663a26e11362ac4))

* feat: private curation cross account kms access

- Production private curation access ([#128](https://github.com/gametimesf/mlplatform/pull/128),
  [`5531cc1`](https://github.com/gametimesf/mlplatform/commit/5531cc12a0073b07750fb9e03a84767138562b8b))

- Remove keyring ([#106](https://github.com/gametimesf/mlplatform/pull/106),
  [`8bf4a75`](https://github.com/gametimesf/mlplatform/commit/8bf4a7515d8511df88a278c385defe4224adc813))

- Staging private curation access ([#127](https://github.com/gametimesf/mlplatform/pull/127),
  [`0818e74`](https://github.com/gametimesf/mlplatform/commit/0818e743cab509e97a9bf04af58a0efee33cca8e))

- Trigger redis flush activity from backfill ingestion
  ([#119](https://github.com/gametimesf/mlplatform/pull/119),
  [`4dbfeed`](https://github.com/gametimesf/mlplatform/commit/4dbfeedbfa5e53e6ac1a32e35dbac3c617317818))

- Update gametime-protos to 0.1.0 ([#87](https://github.com/gametimesf/mlplatform/pull/87),
  [`2d71c2d`](https://github.com/gametimesf/mlplatform/commit/2d71c2d7cc9f3b7fa27bb6a435934cd6da5d31d5))

- Update pricing markups utils functions
  ([`731f9be`](https://github.com/gametimesf/mlplatform/commit/731f9be6b11b7f6f722d283096d18431a8c64214))

- Update release tags
  ([`df7513a`](https://github.com/gametimesf/mlplatform/commit/df7513a612ccb03a68e64151765ad2009e286b93))

### Performance Improvements

- Filter offline S3 reads by entity partition
  ([#121](https://github.com/gametimesf/mlplatform/pull/121),
  [`bcb2580`](https://github.com/gametimesf/mlplatform/commit/bcb25801a40f4b75b67e6795eed351f59f689074))

* perf: filter offline S3 reads by entity partition

* fix: properly rename temp_df

### Refactoring

- Add types to func defs and update get_markups
  ([`8191ce1`](https://github.com/gametimesf/mlplatform/commit/8191ce1b47c676f68837da90be4decc51d4354ab))

- Align feature fetching to use pre-computed states
  ([#114](https://github.com/gametimesf/mlplatform/pull/114),
  [`0c6a83a`](https://github.com/gametimesf/mlplatform/commit/0c6a83afdf51897416b7b4ac2353c895239b219e))

* refactor: align feature fetching to use pre-computed states

* fix: add types-aiobotocore-s3

- Implement historical state lookup for offline features
  ([#117](https://github.com/gametimesf/mlplatform/pull/117),
  [`1d38c6d`](https://github.com/gametimesf/mlplatform/commit/1d38c6daed7e167daa3046cf59395e98a0b676fc))

* refactor: implement historical state lookup for offline features

* chore: add type annotations to offline features tests

* fix: add missing aioboto3 import for type annotation

- Implement unified long-running FamilyPipeline workflow
  ([#132](https://github.com/gametimesf/mlplatform/pull/132),
  [`24c1392`](https://github.com/gametimesf/mlplatform/commit/24c13922d1f41a72ff7a14e5114fa5e3a7757bd3))

- Replace pandas and custom CSV utils with Polars for data IO
  ([#109](https://github.com/gametimesf/mlplatform/pull/109),
  [`a4a7f3d`](https://github.com/gametimesf/mlplatform/commit/a4a7f3d1df35e1f1e69aee0e293f990a64c596bb))

* refactor: Replace pandas with Polars

* fix: add typing

- Split FamilyPipeline workflow into initialization and incremental
  ([#129](https://github.com/gametimesf/mlplatform/pull/129),
  [`958cfb0`](https://github.com/gametimesf/mlplatform/commit/958cfb00a6216af1c9c80fb9fbb20cb15807d57d))

* refactor: split FamilyPipeline workflow into init and incremental

* fix: address PR comments

- Switch offline store to use CSV format and fix tests
  ([#103](https://github.com/gametimesf/mlplatform/pull/103),
  [`13fa468`](https://github.com/gametimesf/mlplatform/commit/13fa46836fe88df1e580c3d00823fa4cae108611))

* refactor: switch offline store to use CSV format and fix tests

* test: add comprehensive integration tests for backfill activity

- Switch primary batch source to Snowflake
  ([#111](https://github.com/gametimesf/mlplatform/pull/111),
  [`f50865e`](https://github.com/gametimesf/mlplatform/commit/f50865ee1bd0f0f3d9d4c71e741ef40561bb7e40))

* refactor: switch primary batch source to Snowflake

* fix: revert package updates

* test: get tests working with Snowflake changes

* docs: add descriptive comments for state serialization functions


## v1.2.0 (2025-03-24)

### Bug Fixes

- Use version tag instead of commit SHA for reusable workflow
  ([#80](https://github.com/gametimesf/mlplatform/pull/80),
  [`bcb1894`](https://github.com/gametimesf/mlplatform/commit/bcb1894df16f5c8924a3c4f75e1cb42f7bef20c8))

- **baseline-images**: Build using code artifact dependency version
  ([#83](https://github.com/gametimesf/mlplatform/pull/83),
  [`a9e459b`](https://github.com/gametimesf/mlplatform/commit/a9e459b0feb90a8f53a06fc5dd703a62d1647e67))

### Chores

- Migrate semantic release workflow to be reusable
  ([#79](https://github.com/gametimesf/mlplatform/pull/79),
  [`9d4793e`](https://github.com/gametimesf/mlplatform/commit/9d4793eb9a0b35c8d2fe40dcf3275e5210a2beca))

* chore: migrate semantic release workflow to be reusable

* test: add this branch to test workflow

* test: correct secret token name

* test: remove test branch from trigger

* fix: pin reusable workflow version

- Pin Gametime python package versions ([#81](https://github.com/gametimesf/mlplatform/pull/81),
  [`115a1e3`](https://github.com/gametimesf/mlplatform/commit/115a1e35f19f0529c7038bf2d16aed686b2f4ddd))

* chore: pin Gametime python package versions

* fix: get credentials for CodeArtifact when building Docker image

* fix: correct AWS permissions for Docker image build

* fix: use specific versions of mlutils and gametime-protos

- Use versioned private Python packages in mlctl
  ([#78](https://github.com/gametimesf/mlplatform/pull/78),
  [`c379c79`](https://github.com/gametimesf/mlplatform/commit/c379c79abeedcb28453fdd799d46ce91d09a826a))

* chore: use versioned private Python packages in mlctl

* chore: use keyring provider to automate credentials to CodeArtifact

### Features

- Add kms access service accounts for production curation
  ([#76](https://github.com/gametimesf/mlplatform/pull/76),
  [`27ff386`](https://github.com/gametimesf/mlplatform/commit/27ff386da3715adaf5209ed945303ac5fdebfa18))

- **baseline-sdk**: Use CodeArtifact version of mlutils
  ([#85](https://github.com/gametimesf/mlplatform/pull/85),
  [`8d0d3ab`](https://github.com/gametimesf/mlplatform/commit/8d0d3ab47739567d045a0c2a1d63def691a95fc8))

- **mlutils**: Update mlutils core dependencies and fix package resolution conflicts when used with
  CodeArtifact. ([#84](https://github.com/gametimesf/mlplatform/pull/84),
  [`2eb73fe`](https://github.com/gametimesf/mlplatform/commit/2eb73feff1c099dfe3f47ef849139f012da3ee62))


## v1.1.0 (2025-03-10)

### Features

- Add kms access service accounts for staging curation
  ([#75](https://github.com/gametimesf/mlplatform/pull/75),
  [`c3c7f45`](https://github.com/gametimesf/mlplatform/commit/c3c7f45561533fc14c925689c6dc3824ad470147))

- Cache github actions ([#69](https://github.com/gametimesf/mlplatform/pull/69),
  [`a2781a7`](https://github.com/gametimesf/mlplatform/commit/a2781a7d57f95cf8a899939be90034700f71f5e5))

- **terraform**: Kms for curation ([#65](https://github.com/gametimesf/mlplatform/pull/65),
  [`340db40`](https://github.com/gametimesf/mlplatform/commit/340db40a3c015cecb420210ddd816f01ecb70bf1))


## v1.0.0 (2025-03-06)

### Bug Fixes

- **comp.baseline-sdk**: Add back serializer imports
  ([#61](https://github.com/gametimesf/mlplatform/pull/61),
  [`bfe6e82`](https://github.com/gametimesf/mlplatform/commit/bfe6e8215175127c731cad7193ea8e93fb18aeaa))

### Chores

- **baseline-images**: Apply idle timeout to prod
  ([#55](https://github.com/gametimesf/mlplatform/pull/55),
  [`25c01e3`](https://github.com/gametimesf/mlplatform/commit/25c01e3dae12821f1b0996b7ce946c8fa44f55b1))

### Continuous Integration

- Add GitHub workflow for pre-commit validation
  ([#56](https://github.com/gametimesf/mlplatform/pull/56),
  [`8969b87`](https://github.com/gametimesf/mlplatform/commit/8969b876dd9a5b711439bbf1eb4fefc0bb7d6dfb))

* ci: add GitHub workflow for pre-commit validation

* fix: ensure all precommit hooks pass

* fix: install terraform/terragrunt

* fix: temporarily disable scope

* fix: simplify pre-commit

* fix: update Python version

* fix: put Python version in string

* fix: add tflint config

- **infra**: Implement simplified commit scope convention
  ([#62](https://github.com/gametimesf/mlplatform/pull/62),
  [`ac7b124`](https://github.com/gametimesf/mlplatform/commit/ac7b1246391b0f8ed4d7187a9e6fd57a84fbe2c8))

- **infra**: Uv pre commit ([#59](https://github.com/gametimesf/mlplatform/pull/59),
  [`2879748`](https://github.com/gametimesf/mlplatform/commit/2879748a4a0ae17463f9422a22ebf3c53f865591))

- **infra.tooling**: Implement dual-axis commit scope taxonomy
  ([#60](https://github.com/gametimesf/mlplatform/pull/60),
  [`9febb64`](https://github.com/gametimesf/mlplatform/commit/9febb64f0ffa86cd7a63a12d579ca034d657ae56))

### Features

- Consolidate ECR Repositories for Baseline Images
  ([#54](https://github.com/gametimesf/mlplatform/pull/54),
  [`e2f05f7`](https://github.com/gametimesf/mlplatform/commit/e2f05f7a50c6f7fa1215f2fc27ba8b7fe28399a1))

- Correct staging baseline service tag ([#52](https://github.com/gametimesf/mlplatform/pull/52),
  [`4cb370f`](https://github.com/gametimesf/mlplatform/commit/4cb370fb8e0251fe4148538b65e473369c8074f5))

- Move baseline to mlplatform ([#44](https://github.com/gametimesf/mlplatform/pull/44),
  [`ac74201`](https://github.com/gametimesf/mlplatform/commit/ac742010a1f6542d046b5270fbd13754df67621e))

feat: move baseline to mlplatform

- Upgrade terraform 1.11 ([#57](https://github.com/gametimesf/mlplatform/pull/57),
  [`25f3ab9`](https://github.com/gametimesf/mlplatform/commit/25f3ab93ff908f3561690665aa55578bae352ac3))

- **baseline-images**: Add idle shutdown configuration
  ([#53](https://github.com/gametimesf/mlplatform/pull/53),
  [`26af153`](https://github.com/gametimesf/mlplatform/commit/26af15331198d359a23ff83806ce8d89de901743))

- **ci**: Add commitlint to enforce conventional commits
  ([#49](https://github.com/gametimesf/mlplatform/pull/49),
  [`5871551`](https://github.com/gametimesf/mlplatform/commit/58715516c5ac5a4409d8cd6aa5c7fc129e9d76a8))

- **repo**: Add semantic release scripts ([#64](https://github.com/gametimesf/mlplatform/pull/64),
  [`9fd6007`](https://github.com/gametimesf/mlplatform/commit/9fd600773ee468ded03acb65c5f78fb57c9a684d))

* ci: implement semantic release GitHub Actions

* feat!: revert mlutils README

* chore: remove test branch

* chore: set all package versions to 1.0.0

* chore: update lock files

* chore: update baseline pyproject names

---------

Co-authored-by: jacobcbeaudin <<EMAIL>>

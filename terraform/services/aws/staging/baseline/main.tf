module "baseline" {
  source = "../../../../modules/aws/baseline"
  providers = {
    aws                          = aws.us-west-1
    aws.databricks_bucket_region = aws.us-west-2
  }

  environment = var.environment
  users = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ]
  cpu_image_tag           = "1.4.0-pr160-ddbc8e5-cpu"
  gpu_image_tag           = "1.4.0-pr160-ddbc8e5-gpu"
  service_image_tag       = "1.8.0-pr160-ddbc8e5"
  notebook_repo           = "gametimesf/notebooks"
  api_credentials_arn     = "arn:aws:secretsmanager:us-west-1:728489771660:secret:baseline-system/staging/MACHINE_API_CREDENTIALS-5rPJVC"
  github_access_token_arn = "arn:aws:secretsmanager:us-west-1:728489771660:secret:baseline-system/staging/GITHUB_PASSWORD-v4nY2s"
  users_arn               = "arn:aws:secretsmanager:us-west-1:728489771660:secret:baseline-system/staging/USERS-Mu2FhG"
  certificate_arn         = "arn:aws:acm:us-west-1:728489771660:certificate/9bec0fe1-dec3-46bc-8bde-678505296ce0"
  vpn_cidr_blocks         = ["10.255.0.0/16"]
  mwaa_vpc_cidr_blocks    = ["10.193.0.0/16"]
  force_new_deployment    = var.force_new_deployment
}

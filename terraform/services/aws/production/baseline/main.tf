module "baseline" {
  source = "../../../../modules/aws/baseline"
  providers = {
    aws                          = aws.us-west-1
    aws.databricks_bucket_region = aws.us-west-2
  }
  environment = var.environment
  users = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ]
  ebs_volume_size_in_gb   = 512
  cpu_image_tag           = "1.4.0-pr160-ddbc8e5-cpu"
  gpu_image_tag           = "1.4.0-pr160-ddbc8e5-gpu"
  service_image_tag       = "1.8.0-pr160-ddbc8e5"
  notebook_repo           = "gametimesf/notebooks"
  api_credentials_arn     = "arn:aws:secretsmanager:us-west-1:728489771660:secret:baseline-system/production/MACHINE_API_CREDENTIALS-px6Xlq"
  github_access_token_arn = "arn:aws:secretsmanager:us-west-1:728489771660:secret:baseline-system/production/GITHUB_PASSWORD-Fs9i5R"
  users_arn               = "arn:aws:secretsmanager:us-west-1:728489771660:secret:baseline-system/production/USERS-14v8Ss"
  certificate_arn         = "arn:aws:acm:us-west-1:728489771660:certificate/474405eb-3d34-475e-a03a-43d54e60c136"
  vpn_cidr_blocks         = ["10.255.0.0/16"]
  mwaa_vpc_cidr_blocks    = ["10.1.0.0/16"]
  force_new_deployment    = var.force_new_deployment
}

[build-system]
build-backend = "hatchling.build"
requires = ["hatchling"]

[dependency-groups]
test = [
  "tabulate>=0.9.0"
]

[project]
authors = [{name = "ML Platform team"}]
dependencies = [
  "aioboto3>=14.1.0",
  "aiokafka>=0.12.0",
  "dacite>=1.9.2",
  "gametime-protos==1.8.0",
  "jinja2>=3.1.6",
  "mlutils[grpc,aiohttp,pandas,sklearn]==1.3.0",
  "polars>=1.27.1",
  "pyarrow>=19.0.1",
  "pyyaml>=6.0.2",
  "redis>=5.2.1",
  "snowflake-connector-python[pandas]>=3.13.2",
  "sqlglot>=26.9.0",
  "temporalio>=1.10.0",
  "types-aiobotocore-s3>=2.21.1"
]
description = "Feature Platform"
name = "prism-service"
readme = "README.md"
requires-python = ">=3.11"
version = "0.1.0"

[project.optional-dependencies]
test = [
  "fakeredis>=2.28.0",
  "grpcio>=1.70.0",
  "kafka-python>=2.0.6",
  "numpy>=2.2.3",
  "pyarrow>=19.0.1",
  "pytest-asyncio>=0.25.3",
  "pytest>=8.3.5",
  "tabulate>=0.9.0",
  "testcontainers[minio,redis]>=4.9.2"
]

[project.scripts]
prism = "src.main:main"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.hatch.metadata]
allow-direct-references = true

[[tool.uv.index]]
name = "codeartifact"
url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/"

[tool.uv.sources]
gametime_protos = {index = "codeartifact"}
mlutils = {index = "codeartifact"}

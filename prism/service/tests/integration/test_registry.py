import pytest

from src.families import (
    Family,
    FamilyConfig,
    SourceConfig,
    BatchSourceConfig,
    FeatureConfig,
    AggregationFunction,
    Registry,
    FamilyStatus,
    FamilyNotFoundError,
    PipelineSettings,
)


def create_sample_family_object(
    name: str, draft: bool = False, existing_id: int | None = None
) -> Family:
    """Helper to create a Family object for testing."""
    batch_config = BatchSourceConfig(
        table="sample_table", late_arriving_data_lag_seconds=3600
    )
    source_config = SourceConfig(batch=batch_config, query="SELECT * FROM {{ ref }}")
    feature_cfg = FeatureConfig(
        column="value", aggregations=[AggregationFunction.AGGREGATION_FUNCTION_COUNT]
    )
    family_cfg = FamilyConfig(
        source=source_config,
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[feature_cfg],
    )
    return Family(
        name=name,
        config=family_cfg,
        draft=draft,
        id=existing_id,
        status=FamilyStatus.FAMILY_STATUS_INITIALIZING
        if not draft
        else FamilyStatus.FAMILY_STATUS_RUNNING,
        pipeline_settings=PipelineSettings(
            num_partitions=1, max_window_seconds=3600, increment_interval_seconds=600
        )
        if not draft
        else None,
    )


@pytest.mark.asyncio
async def test_add_or_update_new_production_family(registry: Registry):
    """Test adding a new production family."""
    family_name = "prod_family_new_registry_test"
    family_obj = create_sample_family_object(name=family_name)

    try:
        await registry.remove(family_name)
    except FamilyNotFoundError:
        pass

    returned_family = await registry.add_or_update_production_family(family_obj)

    assert returned_family is not None
    assert returned_family.name == family_name
    assert returned_family.id is not None
    assert not returned_family.draft
    assert returned_family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING

    fetched_family = await registry.fetch_one(family_name)
    assert fetched_family.id == returned_family.id
    assert not fetched_family.draft
    assert fetched_family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING


@pytest.mark.asyncio
async def test_add_or_update_existing_draft_family(registry: Registry):
    """Test updating an existing draft family to production."""
    family_name = "prod_family_upgrade_draft_registry_test"
    draft_family_for_setup = create_sample_family_object(name=family_name, draft=True)

    try:
        await registry.remove(family_name)
    except FamilyNotFoundError:
        pass

    draft_family_in_reg = await registry.add(draft_family_for_setup)
    draft_id = draft_family_in_reg.id
    assert draft_id is not None
    assert draft_family_in_reg.draft

    prod_family_update_obj = create_sample_family_object(name=family_name)

    returned_family = await registry.add_or_update_production_family(
        prod_family_update_obj
    )

    assert returned_family.id == draft_id
    assert not returned_family.draft
    assert returned_family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING

    fetched_family = await registry.fetch_one(family_name)
    assert fetched_family.id == draft_id
    assert not fetched_family.draft
    assert fetched_family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING
    assert fetched_family.config.source.batch.late_arriving_data_lag_seconds == 3600


@pytest.mark.asyncio
async def test_add_or_update_existing_production_family(registry: Registry):
    """Test updating an existing production family."""
    family_name = "prod_family_update_prod_registry_test"
    initial_prod_family_for_setup = create_sample_family_object(
        name=family_name, draft=False
    )

    try:
        await registry.remove(family_name)
    except FamilyNotFoundError:
        pass

    added_family = await registry.add(initial_prod_family_for_setup)
    original_id = added_family.id
    original_inserted_at = added_family.inserted_at
    await registry.update_status(
        family_name, FamilyStatus.FAMILY_STATUS_RUNNING, "Was running fine"
    )

    updated_batch_config = BatchSourceConfig(
        table="sample_table", late_arriving_data_lag_seconds=7200
    )
    updated_source_config = SourceConfig(
        batch=updated_batch_config, query="SELECT * FROM {{ ref }}"
    )
    updated_feature_cfg = FeatureConfig(
        column="value", aggregations=[AggregationFunction.AGGREGATION_FUNCTION_SUM]
    )
    updated_family_cfg = FamilyConfig(
        source=updated_source_config,
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[updated_feature_cfg],
    )
    family_for_update_obj = Family(name=family_name, config=updated_family_cfg)

    returned_family = await registry.add_or_update_production_family(
        family_for_update_obj
    )

    assert returned_family.id == original_id
    assert not returned_family.draft
    assert returned_family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING
    assert returned_family.inserted_at == original_inserted_at
    assert returned_family.config.source.batch.late_arriving_data_lag_seconds == 7200

    fetched_family = await registry.fetch_one(family_name)
    assert fetched_family.id == original_id
    assert not fetched_family.draft
    assert fetched_family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING
    assert fetched_family.config.source.batch.late_arriving_data_lag_seconds == 7200

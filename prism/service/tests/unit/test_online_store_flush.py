import pytest
import fakeredis.aioredis
from datetime import datetime, timezone, timedelta
from unittest.mock import MagicMock, AsyncMock, patch

from src.stores import OnlineStore, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
from src.families import (
    Family,
    FamilyConfig,
    SourceConfig,
    BatchSourceConfig,
    FeatureConfig,
    FamilyStatus,
)
from src.aggregations import (
    AggregationFunction,
    CountAggregation,
    SumAggregation,
    AggregationDescriptor,
)


# --- Fixtures ---


@pytest.fixture
def mock_redis():
    return fakeredis.aioredis.FakeRedis(decode_responses=False)


@pytest.fixture
def online_store(mock_redis):
    return OnlineStore(mock_redis)


@pytest.fixture
def sample_family():
    """Create a sample family for testing."""
    config = FamilyConfig(
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        source=SourceConfig(
            batch=BatchSourceConfig(
                table="test_events", late_arriving_data_lag_seconds=3600
            )
        ),
        features=[
            FeatureConfig(
                column="value",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_SUM],
            ),
            FeatureConfig(
                column="event_id",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_COUNT],
            ),
        ],
    )
    family = Family(
        name="test_family",
        config=config,
        status=FamilyStatus.FAMILY_STATUS_RUNNING,
    )
    family.id = "test_family_id"

    # Add aggregation descriptors
    family.aggregation_descriptors = [
        AggregationDescriptor(column="value", aggregation=SumAggregation),
        AggregationDescriptor(column="event_id", aggregation=CountAggregation),
    ]

    return family


@pytest.mark.asyncio
async def test_write_rows_returns_empty_list_when_no_rows(online_store, sample_family):
    """Test that write_rows returns an empty list when no rows are provided."""
    result = await online_store.write_rows(sample_family, [])
    assert result == []


@pytest.mark.asyncio
async def test_write_rows_returns_empty_list_when_rows_below_threshold(
    online_store, mock_redis, sample_family
):
    """Test that write_rows returns an empty list when all entities have row counts below the threshold."""
    # Create test data with fewer rows than the threshold
    rows = []
    ts = datetime.now(timezone.utc)

    # Create rows for two different entities, both below threshold
    for entity_id in ["user1", "user2"]:
        for i in range(
            MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5
        ):  # 5 fewer than threshold
            rows.append(
                {
                    "ts": ts + timedelta(seconds=i),
                    "event_id": f"e{i}",
                    "user_id": entity_id,
                    "value": i,
                }
            )

    # Write rows to Redis
    result = await online_store.write_rows(sample_family, rows)

    # Verify no entities need flushing
    assert result == []

    # Verify Redis keys have the expected number of rows
    for entity_id in ["user1", "user2"]:
        key = online_store._key_for(sample_family, entity_id)
        length = await mock_redis.llen(key)
        assert length == MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5


@pytest.mark.asyncio
async def test_write_rows_returns_entities_exceeding_threshold(
    online_store, mock_redis, sample_family
):
    """Test that write_rows returns entities that exceed the threshold."""
    # Create test data with one entity exceeding threshold
    rows = []
    ts = datetime.now(timezone.utc)

    # Entity 1: Exceeds threshold
    for i in range(MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10):
        rows.append(
            {
                "ts": ts + timedelta(seconds=i),
                "event_id": f"e{i}",
                "user_id": "user1",
                "value": i,
            }
        )

    # Entity 2: Below threshold
    for i in range(MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5):
        rows.append(
            {
                "ts": ts + timedelta(seconds=i),
                "event_id": f"e{i}",
                "user_id": "user2",
                "value": i,
            }
        )

    # Mock the pipeline to simulate correct Redis behavior
    mock_pipeline = MagicMock()

    # For user1: exceeds threshold, for user2: doesn't exceed
    execute_results = [
        MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10,  # lpush result for user1
        MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10,  # llen result for user1 (before trim)
        "OK",  # ltrim result for user1
        MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5,  # lpush result for user2
        MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5,  # llen result for user2 (before trim)
        "OK",  # ltrim result for user2
    ]

    mock_pipeline.execute = AsyncMock(return_value=execute_results)
    mock_pipeline.__aenter__ = AsyncMock(return_value=mock_pipeline)
    mock_pipeline.__aexit__ = AsyncMock(return_value=None)
    mock_pipeline.lpush = MagicMock()
    mock_pipeline.llen = MagicMock()
    mock_pipeline.ltrim = MagicMock()

    mock_redis.pipeline = MagicMock(return_value=mock_pipeline)

    # Write rows to Redis
    result = await online_store.write_rows(sample_family, rows)

    # Verify only user1 needs flushing
    assert len(result) == 1
    assert "user1" in result

    # Verify Redis keys have the expected number of rows after the operation
    key1 = online_store._key_for(sample_family, "user1")
    key2 = online_store._key_for(sample_family, "user2")

    # Set up llen to return post-trim values
    async def mock_llen(key):
        if key == key1:
            return MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS  # Trimmed to threshold
        elif key == key2:
            return MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5
        return 0

    mock_redis.llen = AsyncMock(side_effect=mock_llen)

    length1 = await mock_redis.llen(key1)
    assert length1 == MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS  # Trimmed to threshold

    length2 = await mock_redis.llen(key2)
    assert length2 == MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5


@pytest.mark.asyncio
async def test_write_rows_handles_multiple_entities_exceeding_threshold(
    online_store, mock_redis, sample_family
):
    """Test that write_rows correctly identifies multiple entities exceeding the threshold."""
    # Create test data with multiple entities exceeding threshold
    rows = []
    ts = datetime.now(timezone.utc)

    # Create rows for three entities, two exceeding threshold
    entities = {
        "user1": MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10,  # Exceeds
        "user2": MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5,  # Below
        "user3": MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 15,  # Exceeds
    }

    for entity_id, count in entities.items():
        for i in range(count):
            rows.append(
                {
                    "ts": ts + timedelta(seconds=i),
                    "event_id": f"e{i}",
                    "user_id": entity_id,
                    "value": i,
                }
            )

    # Mock the pipeline to simulate correct Redis behavior
    mock_pipeline = MagicMock()

    # Results for user1, user2, user3 in order
    execute_results = [
        MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10,  # lpush result for user1
        MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10,  # llen result for user1 (before trim)
        "OK",  # ltrim result for user1
        MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5,  # lpush result for user2
        MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5,  # llen result for user2 (before trim)
        "OK",  # ltrim result for user2
        MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 15,  # lpush result for user3
        MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 15,  # llen result for user3 (before trim)
        "OK",  # ltrim result for user3
    ]

    mock_pipeline.execute = AsyncMock(return_value=execute_results)
    mock_pipeline.__aenter__ = AsyncMock(return_value=mock_pipeline)
    mock_pipeline.__aexit__ = AsyncMock(return_value=None)
    mock_pipeline.lpush = MagicMock()
    mock_pipeline.llen = MagicMock()
    mock_pipeline.ltrim = MagicMock()

    mock_redis.pipeline = MagicMock(return_value=mock_pipeline)

    # Write rows to Redis
    result = await online_store.write_rows(sample_family, rows)

    # Verify correct entities need flushing
    assert len(result) == 2
    assert "user1" in result
    assert "user3" in result
    assert "user2" not in result

    # Verify Redis keys have the expected number of rows
    async def mock_llen(key):
        entity_id = key.split(":")[-1]
        if entity_id in ["user1", "user3"]:
            return MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS  # Trimmed to threshold
        elif entity_id == "user2":
            return MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5
        return 0

    mock_redis.llen = AsyncMock(side_effect=mock_llen)

    for entity_id in entities:
        key = online_store._key_for(sample_family, entity_id)
        length = await mock_redis.llen(key)
        expected = min(entities[entity_id], MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS)
        assert length == expected


@pytest.mark.asyncio
async def test_write_rows_handles_pipeline_chunking(
    online_store, mock_redis, sample_family
):
    """Test that write_rows correctly handles pipeline chunking for large batches."""
    # Create a large batch that will require multiple pipeline executions
    rows = []
    ts = datetime.now(timezone.utc)

    # Create many entities with varying row counts
    num_entities = 100  # This should force multiple pipeline chunks
    entities_exceeding = []

    for entity_idx in range(num_entities):
        entity_id = f"user{entity_idx}"
        # Every third entity exceeds the threshold
        if entity_idx % 3 == 0:
            row_count = MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 5
            entities_exceeding.append(entity_id)
        else:
            row_count = MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5

        for i in range(row_count):
            rows.append(
                {
                    "ts": ts + timedelta(seconds=i),
                    "event_id": f"e{i}",
                    "user_id": entity_id,
                    "value": i,
                }
            )

    # Mock the pipeline execution to return appropriate lengths
    # We need to create a mock that simulates the pipeline execution
    # and returns the correct lengths for each entity

    # Create a mock pipeline
    mock_pipeline = MagicMock()

    # Set up the execute method to return appropriate values
    # For each entity, we need to return 3 values (lpush result, llen result, ltrim result)
    # The llen result should be the row count for that entity
    execute_results = []
    for entity_idx in range(num_entities):
        entity_id = f"user{entity_idx}"
        if entity_idx % 3 == 0:
            row_count = MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 5
        else:
            row_count = MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5

        # lpush result (number of items in the list after push)
        execute_results.append(row_count)
        # llen result (current length of the list)
        execute_results.append(row_count)
        # ltrim result (always returns "OK")
        execute_results.append("OK")

    mock_pipeline.execute = AsyncMock(return_value=execute_results)
    mock_pipeline.__aenter__ = AsyncMock(return_value=mock_pipeline)
    mock_pipeline.__aexit__ = AsyncMock(return_value=None)

    # Replace the redis.pipeline method
    mock_redis.pipeline = MagicMock(return_value=mock_pipeline)

    # Set up llen to return the expected values after trimming
    async def mock_llen(key):
        entity_id = key.split(":")[-1]
        entity_idx = int(entity_id.replace("user", ""))
        if entity_idx % 3 == 0:
            return MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
        else:
            return MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5

    mock_redis.llen = AsyncMock(side_effect=mock_llen)

    # Write rows to Redis
    result = await online_store.write_rows(sample_family, rows)

    # Verify correct entities need flushing
    assert len(result) == len(entities_exceeding)
    for entity_id in entities_exceeding:
        assert entity_id in result

    # Verify a few Redis keys have the expected number of rows
    for entity_idx in range(0, num_entities, 10):  # Check every 10th entity
        entity_id = f"user{entity_idx}"
        key = online_store._key_for(sample_family, entity_id)
        length = await mock_redis.llen(key)
        if entity_idx % 3 == 0:
            assert length == MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
        else:
            assert length == MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5


@pytest.mark.asyncio
async def test_write_rows_handles_redis_pipeline_error(
    online_store, mock_redis, sample_family
):
    """Test that write_rows handles Redis pipeline execution errors gracefully."""
    # Create test data
    rows = []
    ts = datetime.now(timezone.utc)

    for i in range(10):
        rows.append(
            {
                "ts": ts + timedelta(seconds=i),
                "event_id": f"e{i}",
                "user_id": "user1",
                "value": i,
            }
        )

    # Create a mock pipeline that raises an exception
    mock_pipeline = MagicMock()
    mock_pipeline.execute = AsyncMock(side_effect=Exception("Redis pipeline error"))
    mock_pipeline.__aenter__ = AsyncMock(return_value=mock_pipeline)
    mock_pipeline.__aexit__ = AsyncMock(return_value=None)

    # Replace the redis.pipeline method
    mock_redis.pipeline = MagicMock(return_value=mock_pipeline)

    # Write rows to Redis, should handle the error and return empty list
    with patch("src.stores.logger") as mock_logger:
        result = await online_store.write_rows(sample_family, rows)

    # Verify error was logged
    mock_logger.error.assert_called_with(
        "Error executing final Redis pipeline: Redis pipeline error",
        exc_info=True,
    )

    # Should return empty list on error
    assert result == []

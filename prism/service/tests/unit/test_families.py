import pytest
from datetime import datetime, timezone

from src.families import (
    Family,
    FamilyConfig,
    SourceConfig,
    BatchSourceConfig,
    FeatureConfig,
    AggregationFunction,
    FamilyStatus,
    _parse_aggregation_function_string,
)


@pytest.fixture
def sample_family_config():
    """Configuration for a test family."""
    return FamilyConfig(
        source=SourceConfig(
            batch=BatchSourceConfig(
                table="test_events", late_arriving_data_lag_seconds=3600
            ),
            query="SELECT event_id, ts, user_id, value FROM {{ ref }}",
        ),
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[
            FeatureConfig(
                column="value",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_SUM],
            ),
        ],
    )


@pytest.fixture
def sample_family(sample_family_config) -> Family:
    """Create a sample Family object."""
    return Family(
        id=1,
        name="test_family",
        config=sample_family_config,
        status=FamilyStatus.FAMILY_STATUS_RUNNING,
    )


def test_parse_aggregation_function_string_valid():
    assert (
        _parse_aggregation_function_string("COUNT")
        == AggregationFunction.AGGREGATION_FUNCTION_COUNT
    )
    assert (
        _parse_aggregation_function_string("AGGREGATION_FUNCTION_SUM")
        == AggregationFunction.AGGREGATION_FUNCTION_SUM
    )
    assert (
        _parse_aggregation_function_string("avg")
        == AggregationFunction.AGGREGATION_FUNCTION_AVG
    )
    assert (
        _parse_aggregation_function_string("StdDev")
        == AggregationFunction.AGGREGATION_FUNCTION_STDDEV
    )


def test_parse_aggregation_function_string_invalid():
    assert _parse_aggregation_function_string("NON_EXISTENT_AGG") is None
    assert _parse_aggregation_function_string("") is None


def test_family_from_dict_with_string_aggregations():
    """Test Family.from_dict when aggregations are provided as strings (simulating YAML load)."""
    yaml_data = {
        "name": "yaml_test_family",
        "config": {
            "source": {
                "batch": {"table": "some_table", "late_arriving_data_lag_seconds": 300}
            },
            "id_column": "evt_id",
            "timestamp_column": "evt_ts",
            "identifier_columns": ["entity_id"],
            "features": [
                {"column": "col_a", "aggregations": ["COUNT", "SUM"]},
                {"column": "col_b", "aggregations": ["AGGREGATION_FUNCTION_AVG"]},
            ],
        },
    }

    processed_yaml_data = (
        yaml_data.copy()
    )  # Avoid modifying the original dict for other tests
    for feature_conf_dict in processed_yaml_data["config"]["features"]:
        parsed_aggs_int = []
        for agg_str in feature_conf_dict["aggregations"]:
            enum_val = _parse_aggregation_function_string(agg_str)
            if enum_val:
                parsed_aggs_int.append(enum_val.value)
            else:
                raise ValueError(
                    f"Invalid aggregation string during test setup: {agg_str}"
                )
        feature_conf_dict["aggregations"] = parsed_aggs_int

    family_obj = Family.from_dict(processed_yaml_data)
    assert family_obj.name == "yaml_test_family"
    assert len(family_obj.config.features) == 2
    assert family_obj.config.features[0].column == "col_a"
    assert (
        AggregationFunction.AGGREGATION_FUNCTION_COUNT
        in family_obj.config.features[0].aggregations
    )
    assert (
        AggregationFunction.AGGREGATION_FUNCTION_SUM
        in family_obj.config.features[0].aggregations
    )
    assert family_obj.config.features[1].column == "col_b"
    assert (
        AggregationFunction.AGGREGATION_FUNCTION_AVG
        in family_obj.config.features[1].aggregations
    )


def test_family_to_proto_and_back(sample_family: Family):
    """Test converting a Family object to protobuf and back."""
    proto_msg = sample_family.to_proto()
    rehydrated_family = Family.from_proto(proto_msg)

    assert rehydrated_family.name == sample_family.name
    assert rehydrated_family.config.id_column == sample_family.config.id_column
    assert rehydrated_family.draft == sample_family.draft
    assert rehydrated_family.status == sample_family.status

    assert rehydrated_family.inserted_at.replace(
        microsecond=0
    ) == sample_family.inserted_at.replace(microsecond=0)
    assert rehydrated_family.frontier.replace(
        microsecond=0
    ) == sample_family.frontier.replace(microsecond=0)


def test_family_from_dict_minimal():
    """Test creating a Family object from a minimal dictionary."""
    data = {
        "name": "minimal_family",
        "config": {
            "source": {
                "batch": {
                    "table": "minimal_table",
                    "late_arriving_data_lag_seconds": 60,
                }
            },
            "id_column": "id",
            "timestamp_column": "ts",
            "identifier_columns": ["entity_id"],
            "features": [
                {
                    "column": "value",
                    "aggregations": [
                        AggregationFunction.AGGREGATION_FUNCTION_COUNT.value
                    ],
                }
            ],
        },
    }
    family = Family.from_dict(data)
    assert family.name == "minimal_family"
    assert family.draft is False
    assert family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING
    assert family.frontier == datetime(1, 1, 1, 0, 0, 0, tzinfo=timezone.utc)

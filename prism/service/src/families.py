import asyncio
import json
from dataclasses import dataclass, field, asdict
from datetime import datetime, timezone, MINYEAR
from enum import IntEnum
from functools import cached_property
from typing import Any
import logging

from dacite import from_dict, Config as DaciteConfig
from gametime_protos.mlp.prism.v1.service_pb2 import Family as FamilyProto
from jinja2 import Template
from mlutils.grpc import proto_to_dict, dict_to_proto
from mlutils.utils import remove_none_values

from src.aggregations import (
    AggregationFunction,
    AggregationDescriptor,
    REGISTRY as AGGREGATION_REGISTRY,
)
from src.utils import utcnow, calculate_partition


def datetime_hook(value: Any) -> datetime | None:
    if isinstance(value, datetime):
        return value
    if isinstance(value, str):
        try:
            dt_str = value.replace("Z", "+00:00")
            return datetime.fromisoformat(dt_str)
        except (ValueError, TypeError):
            return None
    return None


DACITE_CONFIG = DaciteConfig(cast=[IntEnum], type_hooks={datetime: datetime_hook})


class FamilyJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


family_json_encoder = FamilyJSONEncoder()


def utcnow_iso():
    return utcnow().isoformat()


class FamilyAlreadyExistsError(RuntimeError):
    """
    Exception to raise when trying to create a family that already exists
    """


class FamilyInvalidError(RuntimeError):
    """
    Exception to raise when family configuration is invalid
    """


class FamilyNotFoundError(RuntimeError):
    """
    Exception to raise when family does not exist
    """


class FamilyStatus(IntEnum):
    FAMILY_STATUS_UNSPECIFIED = 0
    FAMILY_STATUS_INITIALIZING = 1
    FAMILY_STATUS_INITIALIZING_FAILED = 2
    FAMILY_STATUS_BACKFILLING = 3
    FAMILY_STATUS_BACKFILLING_FAILED = 4
    FAMILY_STATUS_RUNNING = 5
    FAMILY_STATUS_RUNNING_FAILED = 6
    FAMILY_STATUS_DELETING = 7
    FAMILY_STATUS_DELETING_FAILED = 8


@dataclass
class FeatureConfig:
    column: str
    aggregations: list[AggregationFunction]


@dataclass
class StreamingSourceConfig:
    topic: str


@dataclass
class BatchSourceConfig:
    table: str
    late_arriving_data_lag_seconds: int = 60 * 60


@dataclass
class SourceConfig:
    batch: BatchSourceConfig
    streaming: StreamingSourceConfig | None = None
    query: str | None = None


@dataclass
class FamilyConfig:
    source: SourceConfig
    id_column: str
    timestamp_column: str
    identifier_columns: list[str]
    features: list[FeatureConfig]


@dataclass
class PipelineSettings:
    num_partitions: int
    max_window_seconds: int
    increment_interval_seconds: int


@dataclass
class Family:
    name: str
    config: FamilyConfig
    id: int | None = None
    draft: bool = False
    inserted_at: datetime = field(default_factory=utcnow)
    status: FamilyStatus = FamilyStatus.FAMILY_STATUS_INITIALIZING
    status_detail: str | None = None
    last_fetched_at: datetime | None = None
    frontier: datetime = field(
        default_factory=lambda: datetime(MINYEAR, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    )
    pipeline_settings: PipelineSettings | None = None

    @classmethod
    def from_proto(cls, message):
        d = proto_to_dict(message, use_integers_for_enums=True)
        return cls.from_dict(d)

    def to_proto(self):
        dict_representation = self.to_dict()
        encoded = family_json_encoder.encode(dict_representation)
        return dict_to_proto(
            json.loads(encoded), FamilyProto(), ignore_unknown_fields=True
        )

    @classmethod
    def from_dict(cls, d):
        if "frontier" not in d or d["frontier"] is None:
            d["frontier"] = datetime(MINYEAR, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
        return from_dict(data_class=cls, data=d, config=DACITE_CONFIG)

    def to_dict(self):
        return asdict(self)

    @cached_property
    def batch_query(self):
        query_template = self.config.source.query or "SELECT * FROM {{ ref }}"
        return Template(query_template).render(ref=self.config.source.batch.table)

    @cached_property
    def streaming_query(self):
        if not self.config.source.streaming:
            return None
        query_template = self.config.source.query or "SELECT * FROM {{ ref }}"
        return Template(query_template).render(ref=self.config.source.streaming.topic)

    @cached_property
    def aggregation_descriptors(self) -> list[AggregationDescriptor]:
        descriptors = []
        if not hasattr(self.config, "features") or not self.config.features:
            return []
        for feature in self.config.features:
            if not hasattr(feature, "aggregations") or not feature.aggregations:
                continue
            for kind_val in feature.aggregations:
                try:
                    kind = AggregationFunction(kind_val)
                    aggregation = AGGREGATION_REGISTRY.get(kind)
                    if aggregation:
                        descriptor = AggregationDescriptor(
                            column=feature.column, aggregation=aggregation
                        )
                        descriptors.append(descriptor)
                except ValueError:
                    pass

        return descriptors

    def pack_identifiers(self, data: dict[str, Any]) -> str:
        values = []
        for col in self.config.identifier_columns:
            value = data.get(col)
            values.append(str(value) if value is not None else "")
        return "|".join(values)

    def calculate_partition(self, packed_identifiers: str, num_partitions: int) -> int:
        """Convenience method to calculate partition directly on the family object."""
        return calculate_partition(packed_identifiers, num_partitions)


def _parse_aggregation_function_string(agg_str: str) -> AggregationFunction | None:
    try:
        # Try direct uppercase match first (e.g., "AGGREGATION_FUNCTION_COUNT")
        return AggregationFunction[agg_str.upper()]
    except KeyError:
        try:
            # Try matching with "AGGREGATION_FUNCTION_" prefix added (e.g., "COUNT" -> "AGGREGATION_FUNCTION_COUNT")
            prefixed_agg_str = f"AGGREGATION_FUNCTION_{agg_str.upper()}"
            return AggregationFunction[prefixed_agg_str]
        except KeyError:
            logging.warning(f"Unknown aggregation function string: {agg_str}")
            return None


class Registry:
    ID_KEY = "families:id"
    FAMILIES_KEY = "families"

    def __init__(self, redis):
        self.redis = redis
        self.redis_json = redis.json()

    def _family_path(self, name):
        safe_name = name
        return f"$.{safe_name}"

    async def add(self, family: Family, allow_update: bool = False) -> Family:
        path = self._family_path(family.name)
        await self.redis_json.set(self.FAMILIES_KEY, "$", {}, nx=True)

        payload = remove_none_values(family.to_dict())
        for key, value in payload.items():
            if isinstance(value, datetime):
                payload[key] = value.isoformat()
            elif isinstance(value, dict):
                if key == "pipeline_settings" and value:
                    payload[key] = {
                        k: (v.isoformat() if isinstance(v, datetime) else v)
                        for k, v in value.items()
                    }
                elif key == "config":
                    if "features" in value and value["features"]:
                        for feature_conf in value["features"]:
                            if (
                                "aggregations" in feature_conf
                                and feature_conf["aggregations"]
                            ):
                                feature_conf["aggregations"] = [
                                    (
                                        agg.value
                                        if isinstance(agg, AggregationFunction)
                                        else int(agg)
                                    )
                                    for agg in feature_conf["aggregations"]
                                ]

        if family.id is None:
            payload["id"] = await self.redis.incr(self.ID_KEY)
            family.id = payload["id"]
        else:
            payload["id"] = family.id

        if allow_update:
            await self.redis_json.set(self.FAMILIES_KEY, path, payload)
        else:
            result = await self.redis_json.set(
                self.FAMILIES_KEY, path, payload, nx=True
            )
            if result is None:
                raise FamilyAlreadyExistsError(f"family '{family.name}' already exists")
        return family

    async def fetch_one(self, name: str) -> Family:
        path = self._family_path(name)
        result = await self.redis_json.get(self.FAMILIES_KEY, path)
        if not result:
            raise FamilyNotFoundError(f"family '{name}' does not exist")
        if isinstance(result, list) and result and isinstance(result[0], dict):
            family_dict = result[0]
            return Family.from_dict(family_dict)
        else:
            root_result = await self.redis_json.get(self.FAMILIES_KEY)
            if isinstance(root_result, dict) and name in root_result:
                return Family.from_dict(root_result[name])
            elif (
                isinstance(root_result, list)
                and root_result
                and isinstance(root_result[0], dict)
                and name in root_result[0]
            ):
                return Family.from_dict(root_result[0][name])
            raise FamilyNotFoundError(
                f"Unexpected data format for family '{name}' in Redis: {result}"
            )

    async def fetch_all(self) -> list[Family]:
        result = await self.redis_json.get(self.FAMILIES_KEY, "$")
        if (
            not result
            or not isinstance(result, list)
            or not isinstance(result[0], dict)
        ):
            return []
        all_families_dict = result[0]
        families = []
        for name, family_dict in all_families_dict.items():
            try:
                if isinstance(family_dict, dict):
                    families.append(Family.from_dict(family_dict))
            except Exception:
                raise
        return families

    async def remove(self, name: str):
        path = self._family_path(name)
        result = await self.redis_json.delete(self.FAMILIES_KEY, path)
        if result == 0:
            raise FamilyNotFoundError(f"family '{name}' does not exist")

    async def _update_family(self, name, fields, nx=False):
        family_path = self._family_path(name)

        update_operations = []
        for key, value in fields.items():
            if isinstance(value, datetime):
                value_to_set = value.isoformat()
            elif isinstance(value, dict):
                value_to_set = {
                    k: (v.isoformat() if isinstance(v, datetime) else v)
                    for k, v in value.items()
                }
            else:
                value_to_set = value

            set_args = {}
            if nx and key == "pipeline_settings":
                set_args["nx"] = True

            update_operations.append(
                self.redis_json.set(
                    self.FAMILIES_KEY, f"{family_path}.{key}", value_to_set, **set_args
                )
            )
        await asyncio.gather(*update_operations)

    async def update_status(self, name, status, status_detail):
        status_val = status.value if isinstance(status, IntEnum) else status
        return await self._update_family(
            name, dict(status=status_val, status_detail=status_detail)
        )

    async def set_pipeline_settings(self, name, settings):
        return await self._update_family(
            name, dict(pipeline_settings=settings), nx=True
        )

    async def update_frontier(self, name: str, frontier_dt: datetime):
        """Updates the frontier timestamp for a specific family."""
        if not isinstance(frontier_dt, datetime) or frontier_dt.tzinfo is None:
            raise ValueError("Frontier must be a timezone-aware datetime")
        utc_frontier = frontier_dt.astimezone(timezone.utc)
        await self._update_family(name, {"frontier": utc_frontier.isoformat()})
        logging.info(
            f"Updated frontier for family '{name}' to {utc_frontier.isoformat()}"
        )

    async def add_or_update_production_family(self, family_from_yaml: Family) -> Family:
        family_from_yaml.draft = False
        family_from_yaml.status = FamilyStatus.FAMILY_STATUS_INITIALIZING

        try:
            existing_family = await self.fetch_one(family_from_yaml.name)
            logging.info(
                f"Family '{family_from_yaml.name}' found in registry. Updating."
            )
            family_from_yaml.id = existing_family.id
            family_from_yaml.inserted_at = existing_family.inserted_at
            await self.remove(existing_family.name)
            updated_family = await self.add(family_from_yaml, allow_update=True)
            logging.info(f"Family '{updated_family.name}' updated successfully.")
            return updated_family
        except FamilyNotFoundError:
            logging.info(
                f"Family '{family_from_yaml.name}' not found. Adding as new production family."
            )
            new_family = await self.add(family_from_yaml)
            logging.info(
                f"Family '{new_family.name}' added successfully with ID {new_family.id}."
            )
            return new_family
        except Exception as e:
            logging.error(
                f"Error in add_or_update_production_family for '{family_from_yaml.name}': {e}",
                exc_info=True,
            )
            raise

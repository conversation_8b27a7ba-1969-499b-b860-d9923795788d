import asyncio
import logging
import sys

import aioboto3
from botocore.exceptions import Client<PERSON>rror
from src.temporal import Client<PERSON>rapper
from temporalio.worker import Worker, WorkflowRunner
from redis.asyncio import Redis

from src.activities import (
    FamilyPipelineActivities,
    FamilyBackfillActivities,
    FamilyIncrementalActivities,
)
from src.persistence_activities import FamilyPersistenceActivities
from src.config import config
from src.families import Registry
from src.shared import TASK_QUEUE_NAME
from src.sources.batch import create as create_batch_source
from src.stores import OfflineStore, OnlineStore
from src.workflows import (
    FamilyPipeline,
    FamilyBackfill,
    FamilyIncrementalProcessingWorkflow,
)
from src.workflow_sandbox_config import CUSTOM_WORKFLOW_RUNNER

logger = logging.getLogger(__name__)


async def create_worker(runner: WorkflowRunner | None = None):
    runner_to_use = runner or CUSTOM_WORKFLOW_RUNNER
    logger.info(f"Creating worker with runner: {type(runner_to_use).__name__}")

    temporal_wrapper = ClientWrapper(config.temporal.url)
    await temporal_wrapper.connect()
    client = temporal_wrapper.client
    redis = Redis(
        host=config.redis.host, port=config.redis.port, decode_responses=False
    )
    redis_decoded = Redis(
        host=config.redis.host, port=config.redis.port, decode_responses=True
    )
    registry = Registry(redis_decoded)
    online_store = OnlineStore(redis)
    batch_source = create_batch_source(
        config.source.batch.kind, config.source.batch.url
    )

    aioboto_session = aioboto3.Session()
    try:
        async with aioboto_session.client(
            "s3", **config.s3.settings
        ) as s3_client_for_check:
            bucket_name = config.s3.bucket
            logger.info(
                f"Ensuring S3 bucket '{bucket_name}' exists at endpoint {config.s3.settings.get('endpoint_url')}..."
            )
            try:
                await s3_client_for_check.head_bucket(Bucket=bucket_name)
                logger.info(f"Bucket '{bucket_name}' already exists.")
            except ClientError as e:
                error_code = e.response.get("Error", {}).get("Code")
                if error_code in ("404", "NoSuchBucket", "NotFound"):
                    logger.info(f"Bucket '{bucket_name}' not found, creating it...")
                    try:
                        create_bucket_config = {}
                        await s3_client_for_check.create_bucket(
                            Bucket=bucket_name, **create_bucket_config
                        )
                        logger.info(f"Successfully created bucket '{bucket_name}'.")
                        await asyncio.sleep(2)
                    except ClientError as create_e:
                        logger.error(
                            f"Failed to create bucket '{bucket_name}': {create_e}",
                            exc_info=True,
                        )
                        raise
                else:
                    logger.error(
                        f"Error checking for bucket '{bucket_name}': {e}", exc_info=True
                    )
                    raise

        offline_store = OfflineStore(
            bucket_name=config.s3.bucket, s3_config=config.s3.settings
        )

        family_persistence_activities = FamilyPersistenceActivities(
            registry=registry,
            online_store=online_store,
            offline_store=offline_store,
        )

        family_pipeline_activities = FamilyPipelineActivities(
            registry=registry,
            batch_source=batch_source,
            temporal_wrapper=temporal_wrapper,
        )

        family_backfill_activities = FamilyBackfillActivities(
            registry=registry,
            batch_source=batch_source,
            offline_store=offline_store,
            online_store=online_store,
            family_persistence_activities=family_persistence_activities,
        )

        family_incremental_activities = FamilyIncrementalActivities(
            registry=registry,
            batch_source=batch_source,
            online_store=online_store,
            offline_store=offline_store,
            persistence_activities=family_persistence_activities,
        )

        activities_to_register = [
            family_pipeline_activities.plan,
            family_pipeline_activities.update_status,
            family_pipeline_activities.create_or_update_schedule,
            family_pipeline_activities.delete_schedule,
            family_pipeline_activities.get_family_config,
            family_backfill_activities.backfill,
            family_incremental_activities.get_frontier,
            family_incremental_activities.set_frontier,
            family_incremental_activities.process_incremental_batch,
            family_persistence_activities.flush_entity_redis_to_s3,
        ]

        workflows_to_register = [
            FamilyPipeline,
            FamilyBackfill,
            FamilyIncrementalProcessingWorkflow,
        ]

        worker = Worker(
            client,
            task_queue=TASK_QUEUE_NAME,
            workflows=workflows_to_register,
            activities=activities_to_register,
            workflow_runner=runner_to_use,
        )

        async def teardown_resources():
            if hasattr(batch_source, "close") and asyncio.iscoroutinefunction(
                batch_source.close
            ):
                try:
                    await batch_source.close()
                    logger.info("Batch source connection closed.")
                except Exception as e:
                    logger.error(f"Error closing batch source: {e}", exc_info=True)
            if redis:
                await redis.aclose()
                logger.info("Redis connection (bytes) closed.")
            if redis_decoded:
                await redis_decoded.aclose()
                logger.info("Redis connection (decoded) closed.")

        shutdown_handlers = [teardown_resources]
        return worker, shutdown_handlers

    except Exception as setup_err:
        logger.error(f"Fatal error during worker setup: {setup_err}", exc_info=True)
        if "redis" in locals() and redis:
            await redis.aclose()
        if "redis_decoded" in locals() and redis_decoded:
            await redis_decoded.aclose()
        raise setup_err


async def main():
    worker = None
    on_shutdown_handlers = []
    try:
        worker, on_shutdown_handlers = await create_worker()
        logger.info("Temporal worker created successfully. Starting run...")
        await worker.run()
        logger.info("Temporal worker run finished.")
    except asyncio.CancelledError:
        logger.info("Worker run cancelled.")
    except Exception as e:
        logger.critical(f"Worker failed to start or run: {e}", exc_info=True)
    finally:
        logger.info("Running shutdown handlers...")
        for handler in on_shutdown_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler()
                else:
                    handler()
            except Exception as shutdown_e:
                logger.error(
                    f"Error during shutdown handler {getattr(handler, '__name__', repr(handler))}: {shutdown_e}",
                    exc_info=True,
                )
        if worker and not worker.is_shutdown:
            logger.info("Attempting final worker shutdown...")
            await worker.shutdown()
            logger.info("Final worker shutdown complete.")
        logger.info("Shutdown handlers complete.")


if __name__ == "__main__":
    logging.basicConfig(
        stream=sys.stdout,
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    )
    logger.info("Starting worker directly.")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Worker interrupted by KeyboardInterrupt.")

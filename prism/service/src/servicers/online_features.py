import json
import math
import async<PERSON>
import pyarrow as pa
import polars as pl
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timezone, timedelta
from collections import defaultdict
from src.shared import Logger

from grpc import StatusCode

from gametime_protos.mlp.prism.v1.service_pb2 import (
    OnlineFeaturesServiceFetchRequest,
    OnlineFeaturesServiceFetchResponse,
    FeatureRequest,
)
from gametime_protos.mlp.prism.v1.service_pb2_grpc import (
    OnlineFeaturesServiceServicer,
    add_OnlineFeaturesServiceServicer_to_server,
)

from src.families import Registry, FamilyNotFoundError, Family
from src.stores import OnlineStore, OfflineStore, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
from src.aggregations import (
    AggregationFunction,
    REGISTRY as AGGREGATION_REGISTRY,
    Aggregation,
    AggregationDescriptor,
)
from src.servicers.offline_features import (
    parse_window_proto,
    find_row_at_or_before_polars,
    get_state_from_row,
)
from src.shared import get_logger, log_call
from src.utils import utcnow, calculate_partition
from src.activities import ensure_utc

register_servicer = add_OnlineFeaturesServiceServicer_to_server
logger = get_logger(__name__)


async def _fetch_entity_data_for_range(
    family: Family,
    packed_identifiers: str,
    start_ts_needed: datetime,
    end_ts_needed: datetime,
    online_store: OnlineStore,
    offline_store: OfflineStore,
    fetch_logger: Logger,
    redis_timeout: float = 5.0,
    s3_timeout: float = 10.0,
) -> pl.DataFrame:
    """
    Fetches and combines entity data from Redis and S3 for a given time range.

    Args:
        family: The family object.
        packed_identifiers: The packed entity identifiers.
        start_ts_needed: The earliest timestamp required.
        end_ts_needed: The latest timestamp required.
        online_store: The OnlineStore instance.
        offline_store: The OfflineStore instance.
        fetch_logger: Logger instance for contextual logging.
        redis_timeout: Timeout in seconds for Redis operations.
        s3_timeout: Timeout in seconds for S3 operations.

    Returns:
        A Polars DataFrame containing the combined, sorted, and filtered data,
        or an empty DataFrame if no data is found or an error occurs.
    """
    fetch_logger.info(
        f"Fetching data between {start_ts_needed.isoformat()} and {end_ts_needed.isoformat()}"
    )

    redis_df = pl.DataFrame()
    oldest_redis_ts: datetime | None = None
    try:
        redis_rows_dicts = await asyncio.wait_for(
            online_store.read_recent_rows(
                family,
                packed_identifiers,
                num_rows=MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
                * 2,  # TODO: document reason for 2
            ),
            timeout=redis_timeout,
        )

        if redis_rows_dicts:
            max_rows = 1000
            if len(redis_rows_dicts) > max_rows:
                fetch_logger.warning(
                    f"Limiting Redis rows from {len(redis_rows_dicts)} to {max_rows}"
                )
                redis_rows_dicts = redis_rows_dicts[:max_rows]

            redis_df = pl.DataFrame(redis_rows_dicts, strict=False)
            ts_col = family.config.timestamp_column
            if ts_col in redis_df.columns:
                redis_df = redis_df.with_columns(
                    pl.col(ts_col)
                    .map_elements(
                        ensure_utc,
                        return_dtype=pl.Datetime(time_unit="us", time_zone="UTC"),
                    )
                    .alias(ts_col)
                ).filter(pl.col(ts_col).is_not_null())

                if not redis_df.is_empty():
                    redis_df = redis_df.sort(ts_col)
                    oldest_redis_ts = redis_df.item(0, ts_col)
                    fetch_logger.debug(
                        f"Fetched {len(redis_df)} rows from Redis. Oldest TS: {oldest_redis_ts.isoformat() if oldest_redis_ts else 'N/A'}"
                    )
                else:
                    fetch_logger.debug("Redis data empty after timestamp conversion.")
            else:
                fetch_logger.warning(
                    f"Timestamp column '{ts_col}' not found in Redis data."
                )
                redis_df = pl.DataFrame()
        else:
            fetch_logger.debug("No data found in Redis.")
    except asyncio.TimeoutError:
        fetch_logger.warning(f"Timeout ({redis_timeout}s) reading from Redis")
        redis_df = pl.DataFrame()
    except Exception as e:
        fetch_logger.error(f"Error reading from Redis: {e}", exc_info=True)
        redis_df = pl.DataFrame()

    s3_df = pl.DataFrame()
    needs_s3 = False
    s3_end_date = None

    max_s3_days = 7
    if start_ts_needed < end_ts_needed - timedelta(days=max_s3_days):
        original_start = start_ts_needed
        start_ts_needed = end_ts_needed - timedelta(days=max_s3_days)
        fetch_logger.warning(
            f"Limiting S3 fetch window from {original_start.isoformat()} to {start_ts_needed.isoformat()} (max {max_s3_days} days)"
        )

    if oldest_redis_ts is None:
        needs_s3 = True
        s3_end_date = end_ts_needed.date()
        fetch_logger.info("No Redis data found, attempting full range fetch from S3.")
    elif oldest_redis_ts > start_ts_needed:
        needs_s3 = True
        s3_end_date = (oldest_redis_ts - timedelta(microseconds=1)).date()
        fetch_logger.info(
            f"Oldest Redis TS ({oldest_redis_ts.isoformat()}) is after start needed ({start_ts_needed.isoformat()}). Fetching S3 data up to {s3_end_date}."
        )
    else:
        fetch_logger.info(
            "Redis data covers the required start time. No S3 fetch needed."
        )

    if needs_s3 and family.pipeline_settings:
        try:
            partition_num = calculate_partition(
                packed_identifiers, family.pipeline_settings.num_partitions
            )
            s3_start_date = start_ts_needed.date()

            if s3_end_date < s3_start_date:
                fetch_logger.warning(
                    f"S3 calculated end date {s3_end_date} is before start date {s3_start_date}. Skipping S3 fetch."
                )
            else:
                fetch_logger.debug(
                    f"Fetching S3 data for partition {partition_num} between {s3_start_date} and {s3_end_date}"
                )
                try:
                    s3_df_optional = await asyncio.wait_for(
                        offline_store.read_specific_partition_in_range(
                            family, partition_num, s3_start_date, s3_end_date
                        ),
                        timeout=s3_timeout,
                    )

                    if s3_df_optional is not None and not s3_df_optional.is_empty():
                        s3_df = s3_df_optional

                        fetch_logger.info(
                            f"Successfully fetched {len(s3_df)} rows from S3."
                        )
                    else:
                        fetch_logger.info(
                            f"No data returned from S3 for partition {partition_num} in range {s3_start_date}-{s3_end_date}."
                        )
                except asyncio.TimeoutError:
                    fetch_logger.warning(f"Timeout ({s3_timeout}s) reading from S3")
                    s3_df = pl.DataFrame()
        except Exception as e:
            fetch_logger.error(f"Error reading from S3: {e}", exc_info=True)
            s3_df = pl.DataFrame()
    elif needs_s3 and not family.pipeline_settings:
        fetch_logger.error(
            "Cannot fetch from S3: Family pipeline settings are missing."
        )

    combined_df: pl.DataFrame
    if not redis_df.is_empty() and not s3_df.is_empty():
        try:
            s3_cols = set(s3_df.columns)
            redis_cols = set(redis_df.columns)
            all_cols = s3_cols.union(redis_cols)

            if s3_cols != all_cols:
                for col in all_cols - s3_cols:
                    col_type = redis_df[col].dtype if col in redis_cols else pl.Utf8
                    s3_df = s3_df.with_columns(pl.lit(None, dtype=col_type).alias(col))
            if redis_cols != all_cols:
                for col in all_cols - redis_cols:
                    col_type = s3_df[col].dtype if col in s3_cols else pl.Utf8
                    redis_df = redis_df.with_columns(
                        pl.lit(None, dtype=col_type).alias(col)
                    )

            s3_df = s3_df.select(sorted(all_cols))
            redis_df = redis_df.select(sorted(all_cols))

            # TODO: can we simplify this so it's just this line?
            combined_df = pl.concat([s3_df, redis_df], how="vertical_relaxed")
            fetch_logger.debug(
                f"Combined S3 ({len(s3_df)}) and Redis ({len(redis_df)}) data. Total: {len(combined_df)} rows."
            )
        except Exception as concat_err:
            fetch_logger.error(
                f"Error concatenating Redis and S3 data: {concat_err}. Using only Redis data.",
                exc_info=True,
            )
            combined_df = redis_df
    elif not redis_df.is_empty():
        combined_df = redis_df
        fetch_logger.debug("Using only Redis data.")
    elif not s3_df.is_empty():
        combined_df = s3_df
        fetch_logger.debug("Using only S3 data.")
    else:
        combined_df = pl.DataFrame()
        fetch_logger.warning("No data found from either Redis or S3.")

    if not combined_df.is_empty():
        ts_col = family.config.timestamp_column
        id_col = family.config.id_column
        if ts_col not in combined_df.columns:
            fetch_logger.error(
                f"Timestamp column '{ts_col}' missing after combining. Returning empty."
            )
            return pl.DataFrame()
        if id_col not in combined_df.columns:
            fetch_logger.warning(
                f"ID column '{id_col}' missing after combining. Cannot deduplicate."
            )
            combined_df = combined_df.sort(ts_col)
        else:
            combined_df = combined_df.sort(ts_col)
            combined_df = (
                combined_df.sort(ts_col, id_col, descending=[True, False])
                .unique(subset=[id_col], keep="first", maintain_order=False)
                .sort(ts_col)
            )
            fetch_logger.debug(
                f"Deduplicated combined data based on '{id_col}'. Rows remaining: {len(combined_df)}"
            )

        combined_df = combined_df.filter(
            (pl.col(ts_col) >= start_ts_needed) & (pl.col(ts_col) <= end_ts_needed)
        )
        fetch_logger.info(
            f"Filtered combined data to time range. Final rows: {len(combined_df)}"
        )

    return combined_df


class Servicer(OnlineFeaturesServiceServicer):
    def __init__(
        self, registry: Registry, online_store: OnlineStore, offline_store: OfflineStore
    ):
        self.registry = registry
        self.online_store = online_store
        self.offline_store = offline_store

    @log_call(logger=logger)
    async def Fetch(self, request: OnlineFeaturesServiceFetchRequest, context):
        """
        Fetches specific online features for a single entity.

        - Handles multiple feature requests for one entity.
        - Checks Redis first.
        - If needed (for windowed features requiring older data), fetches from S3.
        - Combines data sources.
        - Computes point-in-time or windowed features based on the combined data.
        - Encodes results as a JSON object within the response 'features' bytes field.
        - Restricts fetching from families in 'draft' mode.
        """
        req_logger = logger.with_context(
            request_id=getattr(context, "request_id", "N/A"),
            feature_count=len(request.feature_requests),
            identifiers=str(request.identifiers),
        )
        req_logger.info("Processing online features fetch request")

        if not request.identifiers:
            await context.abort(
                StatusCode.INVALID_ARGUMENT, "identifiers map cannot be empty."
            )
            return OnlineFeaturesServiceFetchResponse()
        if not request.feature_requests:
            await context.abort(
                StatusCode.INVALID_ARGUMENT, "feature_requests map cannot be empty."
            )
            return OnlineFeaturesServiceFetchResponse()

        requests_by_family: dict[str, list[tuple[str, FeatureRequest]]] = defaultdict(
            list
        )
        family_cache: dict[str, Family] = {}
        min_start_ts_by_family: dict[str, datetime] = {}
        current_time = utcnow()

        for req_key, feat_req in request.feature_requests.items():
            if not feat_req.family:
                req_logger.warning(
                    f"Skipping feature request '{req_key}' due to missing family name."
                )
                continue
            requests_by_family[feat_req.family].append((req_key, feat_req))

            window = parse_window_proto(feat_req.aggregation.window)
            if window:
                start_ts = current_time - window
                if (
                    feat_req.family not in min_start_ts_by_family
                    or start_ts < min_start_ts_by_family[feat_req.family]
                ):
                    min_start_ts_by_family[feat_req.family] = start_ts

        computed_features = {}
        latest_timestamp_found: datetime | None = None
        entity_family_data: dict[str, pl.DataFrame] = {}

        for family_name, family_requests in requests_by_family.items():
            family_logger = req_logger.with_context(family=family_name)
            family_logger.debug(
                f"Processing {len(family_requests)} requests for family."
            )

            try:
                if family_name not in family_cache:
                    family = await self.registry.fetch_one(family_name)
                    family_cache[family_name] = family
                else:
                    family = family_cache[family_name]

                if family.draft:
                    family_logger.warning(
                        f"Attempt to fetch online features from draft family '{family_name}'. "
                        f"Request will be aborted for features related to this family."
                    )
                    for req_key_for_draft, _ in family_requests:
                        computed_features[req_key_for_draft] = None
                    continue

            except FamilyNotFoundError:
                family_logger.warning(
                    "Family not found. Skipping requests for this family."
                )
                for req_key, _ in family_requests:
                    computed_features[req_key] = None
                continue
            except Exception as e:
                family_logger.error(f"Error fetching family: {e}", exc_info=True)
                for req_key, _ in family_requests:
                    computed_features[req_key] = None
                continue

            try:
                identifier_dict = {
                    col: request.identifiers[col]
                    for col in family.config.identifier_columns
                }
                packed_identifiers = family.pack_identifiers(identifier_dict)
            except KeyError as e:
                family_logger.warning(
                    f"Missing identifier '{e}' in request needed by family. Skipping requests for this family."
                )
                for req_key, _ in family_requests:
                    computed_features[req_key] = None
                continue
            except Exception as e:
                family_logger.warning(f"Failed to pack identifiers: {e}")
                for req_key, _ in family_requests:
                    computed_features[req_key] = None
                continue

            family_key = f"{family_name}:{packed_identifiers}"
            # if family_key not in entity_family_data:
            min_start_ts = min_start_ts_by_family.get(
                family_name, current_time - timedelta(days=1)
            )
            entity_family_df = await _fetch_entity_data_for_range(
                family=family,
                packed_identifiers=packed_identifiers,
                start_ts_needed=min_start_ts,
                end_ts_needed=current_time,
                online_store=self.online_store,
                offline_store=self.offline_store,
                fetch_logger=family_logger,
            )
            entity_family_data[family_key] = entity_family_df
            # else:
            #     entity_family_df = entity_family_data[family_key]

            # TODO: cleanup all timestamp handling
            if not entity_family_df.is_empty():
                ts_col = family.config.timestamp_column
                if ts_col in entity_family_df.columns:
                    try:
                        if entity_family_df[ts_col].dtype.is_temporal():
                            last_ts_in_df = entity_family_df[ts_col].max()
                            if isinstance(last_ts_in_df, datetime):
                                if (
                                    latest_timestamp_found is None
                                    or last_ts_in_df > latest_timestamp_found
                                ):
                                    latest_timestamp_found = last_ts_in_df
                    except Exception as ts_err:
                        family_logger.warning(
                            f"Could not determine max timestamp from fetched data: {ts_err}"
                        )

            for req_key, feat_req_proto in family_requests:
                feature_logger = family_logger.with_context(feature_req=req_key)
                computed_value = None

                if entity_family_df is None:
                    # TODO: if data fetch fails, fail request
                    feature_logger.error(
                        "Data fetch failed previously. Result is null."
                    )
                    computed_features[req_key] = None
                    continue
                if entity_family_df.is_empty():
                    feature_logger.debug(
                        "No historical data available for computation. Result is null."
                    )
                    computed_features[req_key] = None
                    continue

                try:
                    ts_col = family.config.timestamp_column

                    agg_func_enum = feat_req_proto.aggregation.function
                    internal_agg_enum = AggregationFunction(agg_func_enum)
                    aggregation_impl = AGGREGATION_REGISTRY.get(internal_agg_enum)
                    if not aggregation_impl:
                        raise ValueError(
                            f"Unsupported aggregation function enum value: {agg_func_enum}"
                        )

                    descriptor = _find_aggregation_descriptor(
                        family,
                        feat_req_proto,
                        aggregation_impl,
                        req_key,
                        feature_logger,
                    )
                    if not descriptor:
                        computed_features[req_key] = None
                        continue

                    window = parse_window_proto(feat_req_proto.aggregation.window)

                    target_ts = current_time
                    end_row_df = find_row_at_or_before_polars(
                        entity_family_df, target_ts, ts_col
                    )

                    if end_row_df is None:
                        feature_logger.debug(
                            f"No data found at or before {target_ts} in combined data. Result is null."
                        )
                        computed_value = None
                    else:
                        end_state = get_state_from_row(end_row_df, descriptor.state_key)

                        if window:
                            start_ts = target_ts - window
                            feature_logger.debug(
                                f"Windowed feature: target={target_ts}, window={window}, start={start_ts}"
                            )

                            start_row_df = find_row_at_or_before_polars(
                                entity_family_df, start_ts, ts_col
                            )
                            start_state = (
                                get_state_from_row(start_row_df, descriptor.state_key)
                                if start_row_df is not None
                                else None
                            )

                            if start_state is None and start_row_df is not None:
                                feature_logger.warning(
                                    f"Found start row at {start_row_df.item(0, ts_col)} but state key '{descriptor.state_key}' was missing/null."
                                )
                            elif start_state is None:
                                feature_logger.debug(
                                    f"No row found at or before start_ts {start_ts} in combined data, using initial state for start."
                                )

                            try:
                                prepared_states = _prepare_arrow_state(
                                    aggregation_impl.compute_windowed,
                                    start_state,
                                    end_state,
                                    is_windowed=True,
                                )
                                if prepared_states == (None, None):
                                    raise ValueError("Failed to prepare Arrow states.")
                                start_components, end_components = prepared_states

                                feature_logger.debug(
                                    f"Calling compute_windowed with start: {start_state}, end: {end_state}"
                                )

                                if aggregation_impl.__name__ == "SumAggregation":
                                    # TODO: is this check really necessary?
                                    # There should be no branching on Aggregation types
                                    if (
                                        len(start_components) == 1
                                        and len(end_components) == 1
                                    ):
                                        start_count_arr = pa.array(
                                            [1 if start_state else 0], type=pa.int64()
                                        )
                                        end_count_arr = pa.array(
                                            [1 if end_state else 0], type=pa.int64()
                                        )
                                        pa_result = aggregation_impl.compute_windowed(
                                            start_components[0],
                                            start_count_arr,
                                            end_components[0],
                                            end_count_arr,
                                        )
                                    else:
                                        pa_result = aggregation_impl.compute_windowed(
                                            *start_components, *end_components
                                        )
                                else:
                                    pa_result = aggregation_impl.compute_windowed(
                                        *start_components, *end_components
                                    )

                                if pa_result is not None:
                                    if hasattr(pa_result, "as_py"):
                                        computed_value = pa_result.as_py()
                                        feature_logger.debug(
                                            f"Computed windowed scalar value: {computed_value}"
                                        )
                                    elif len(pa_result) > 0:
                                        is_null = getattr(
                                            pa_result[0], "is_null", False
                                        )
                                        if not is_null:
                                            computed_value = pa_result[0].as_py()
                                            feature_logger.debug(
                                                f"Computed windowed value: {computed_value}"
                                            )
                                        else:
                                            feature_logger.debug(
                                                "Windowed computation result was null."
                                            )
                                            computed_value = None
                                    else:
                                        feature_logger.debug(
                                            "Windowed computation result was empty."
                                        )
                                        computed_value = None
                                else:
                                    feature_logger.debug(
                                        "Windowed computation result was None."
                                    )
                                    computed_value = None
                            except Exception as compute_err:
                                feature_logger.error(
                                    f"Error during compute_windowed: {compute_err}",
                                    exc_info=True,
                                )
                                computed_value = None

                        else:
                            end_ts_found = end_row_df.item(0, ts_col)
                            feature_logger.debug(
                                f"Point-in-time feature using state from {end_ts_found.isoformat() if isinstance(end_ts_found, datetime) else end_ts_found}"
                            )
                            if end_state is None:
                                feature_logger.warning(
                                    f"End row found but state key '{descriptor.state_key}' was missing/null."
                                )
                                computed_value = None
                            else:
                                try:
                                    prepared_states = _prepare_arrow_state(
                                        aggregation_impl.compute,
                                        end_state,
                                        is_windowed=False,
                                    )
                                    if prepared_states == (None,):
                                        raise ValueError(
                                            "Failed to prepare Arrow states."
                                        )
                                    state_components = prepared_states[0]

                                    feature_logger.debug(
                                        f"Calling compute with state: {end_state}"
                                    )

                                    if (
                                        aggregation_impl.__name__ == "SumAggregation"
                                        and len(state_components) == 1
                                    ):
                                        count_arr = pa.array(
                                            [1 if end_state else 0], type=pa.int64()
                                        )
                                        pa_result = aggregation_impl.compute(
                                            state_components[0], count_arr
                                        )
                                    else:
                                        pa_result = aggregation_impl.compute(
                                            *state_components
                                        )

                                    if pa_result is not None:
                                        if hasattr(pa_result, "as_py"):
                                            computed_value = pa_result.as_py()
                                            feature_logger.debug(
                                                f"Computed point-in-time scalar value: {computed_value}"
                                            )
                                        elif len(pa_result) > 0:
                                            is_null = getattr(
                                                pa_result[0], "is_null", False
                                            )
                                            if not is_null:
                                                computed_value = pa_result[0].as_py()
                                                feature_logger.debug(
                                                    f"Computed point-in-time value: {computed_value}"
                                                )
                                            else:
                                                feature_logger.debug(
                                                    "Point-in-time computation result was null."
                                                )
                                                computed_value = None
                                        else:
                                            feature_logger.debug(
                                                "Point-in-time computation result was empty."
                                            )
                                            computed_value = None
                                    else:
                                        feature_logger.debug(
                                            "Point-in-time computation result was None."
                                        )
                                        computed_value = None
                                except Exception as compute_err:
                                    feature_logger.error(
                                        f"Error during compute: {compute_err}",
                                        exc_info=True,
                                    )
                                    computed_value = None

                    computed_features[req_key] = computed_value

                except Exception as inner_e:
                    feature_logger.error(
                        f"Error processing feature request: {inner_e}", exc_info=True
                    )
                    computed_features[req_key] = None

        try:
            serializable_features = {}
            for k, v in computed_features.items():
                if isinstance(v, float) and (math.isnan(v) or math.isinf(v)):
                    serializable_features[k] = None
                else:
                    serializable_features[k] = v
            features_bytes = json.dumps(serializable_features).encode("utf-8")
        except Exception as json_err:
            req_logger.error(
                f"Failed to JSON encode computed features: {json_err}", exc_info=True
            )
            await context.abort(StatusCode.INTERNAL, "Failed to format response.")
            return OnlineFeaturesServiceFetchResponse()

        frontier_proto = Timestamp()
        if latest_timestamp_found:
            if latest_timestamp_found.tzinfo is None:
                latest_timestamp_found = latest_timestamp_found.replace(
                    tzinfo=timezone.utc
                )
            elif latest_timestamp_found.tzinfo != timezone.utc:
                latest_timestamp_found = latest_timestamp_found.astimezone(timezone.utc)
            try:
                frontier_proto.FromDatetime(latest_timestamp_found)
            except ValueError as ts_conv_err:
                req_logger.error(
                    f"Failed to convert latest timestamp {latest_timestamp_found} to proto: {ts_conv_err}"
                )
        else:
            req_logger.debug("No latest timestamp found for frontier.")

        req_logger.info(
            f"Responding to Online Fetch request. Result keys: {list(computed_features.keys())}"
        )
        return OnlineFeaturesServiceFetchResponse(
            features=features_bytes, frontier=frontier_proto
        )


def _prepare_arrow_state(
    compute_method: callable,
    *state_values: tuple[float | None, ...] | float | list | None,
    is_windowed: bool = False,
) -> tuple[list[pa.Array] | None, ...] | tuple[None, ...]:
    """
    Prepares state values (which can be None or tuples) for PyArrow compute functions.
    Handles None inputs by creating pa.nulls arrays.
    (Implementation Assumed Present - same as in previous offline_features)
    """
    prepare_logger = logger.with_context(
        method_name=compute_method.__name__, is_windowed=is_windowed
    )
    num_expected_args = compute_method.__code__.co_argcount - 1

    if is_windowed:
        num_expected_per_state = num_expected_args // 2
        if num_expected_args % 2 != 0:
            prepare_logger.error(
                f"Windowed compute method has odd number of state args: {num_expected_args}"
            )
            return None, None

        start_state_val = state_values[0] if len(state_values) > 0 else None
        end_state_val = state_values[1] if len(state_values) > 1 else None

        start_components = _arrowfy_single_state(
            start_state_val,
            num_expected_per_state,
            compute_method.__name__,
            "start",
            prepare_logger,
        )
        end_components = _arrowfy_single_state(
            end_state_val,
            num_expected_per_state,
            compute_method.__name__,
            "end",
            prepare_logger,
        )

        if start_components is None or end_components is None:
            return None, None

        return start_components, end_components
    else:
        state_val = state_values[0] if len(state_values) > 0 else None

        components = _arrowfy_single_state(
            state_val,
            num_expected_args,
            compute_method.__name__,
            "state",
            prepare_logger,
        )

        if components is None:
            return (None,)

        return (components,)


def _arrowfy_single_state(
    state_value: tuple[float, ...] | float | list | None,
    num_expected_components: int,
    method_name: str,
    state_desc: str,
    parent_logger: Logger,
) -> list[pa.Array] | None:
    """Converts a single state value (primitive, tuple/list, or None) into a list of Arrow arrays."""
    state_logger = parent_logger.with_context(state_desc=state_desc)

    if state_value is None:
        if num_expected_components == 0:
            return []

        return [pa.nulls(1, type=pa.float64())] * num_expected_components

    components = []
    try:
        if isinstance(state_value, (list, tuple)):
            if len(state_value) == num_expected_components:
                arrow_components = []
                for i, v in enumerate(state_value):
                    try:
                        if v is None:
                            arrow_components.append(pa.nulls(1, type=pa.float64()))
                        else:
                            try:
                                arr = pa.array([float(v)], type=pa.float64())
                            except (ValueError, TypeError):
                                arr = pa.array([v])
                            arrow_components.append(arr)
                    except Exception as component_err:
                        state_logger.error(
                            f"Error converting state component {i} ({v}) to Arrow array for {method_name}: {component_err}",
                            exc_info=True,
                        )
                        return None
                components = arrow_components

            else:
                state_logger.error(
                    f"State arity mismatch for {method_name}: expected {num_expected_components} components, "
                    f"got tuple/list of length {len(state_value)}: {state_value}"
                )
                return None
        elif num_expected_components == 1:
            try:
                if hasattr(state_value, "to_list") and callable(
                    getattr(state_value, "to_list")
                ):
                    values_list = state_value.to_list()
                    if values_list and len(values_list) > 0:
                        state_value = values_list[0]
                    else:
                        state_value = 0.0

                try:
                    arr = pa.array([float(state_value)], type=pa.float64())
                except (ValueError, TypeError):
                    arr = pa.array([state_value])
                components = [arr]

            except Exception as single_comp_err:
                state_logger.error(
                    f"Error converting single state value {state_value} to Arrow array for {method_name}: {single_comp_err}",
                    exc_info=True,
                )
                arr = pa.array([0.0], type=pa.float64())
                components = [arr]
        else:
            state_logger.error(
                f"State type/arity mismatch for {method_name}: expected {num_expected_components} components, "
                f"got single value {state_value} ({type(state_value)})"
            )
            return None
    except Exception as arrow_err:
        state_logger.error(
            f"Failed to convert state value {state_value} to Arrow array(s) for {method_name}: {arrow_err}",
            exc_info=True,
        )
        return None
    return components


def _find_aggregation_descriptor(
    family: Family,
    feat_req_proto: FeatureRequest,
    aggregation_impl: type[Aggregation],
    req_key: str,
    req_logger: Logger,
) -> AggregationDescriptor | None:
    """Finds the matching AggregationDescriptor (modified for online request structure)."""
    column_name = feat_req_proto.column
    if not column_name:
        req_logger.error(
            f"Missing column name in feature request '{req_key}' for family '{family.name}'."
        )
        return None

    descriptors = family.aggregation_descriptors
    if not descriptors:
        req_logger.error(f"No aggregation descriptors found for family {family.name}")
        return None

    descriptor = next(
        (
            d
            for d in descriptors
            if d.column == column_name and d.aggregation == aggregation_impl
        ),
        None,
    )
    if not descriptor:
        req_logger.error(
            f"Aggregation descriptor not found for column '{column_name}' "
            f"and aggregation type '{aggregation_impl.SHORT_NAME}' in family '{family.name}'. "
            f"Check if feature '{column_name}' with aggregation '{aggregation_impl.SHORT_NAME}' "
            f"is defined in the family config. Request key: '{req_key}'"
        )
    return descriptor

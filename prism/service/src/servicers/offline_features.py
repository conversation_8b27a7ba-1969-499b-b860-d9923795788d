import uuid
import pyarrow as pa
import polars as pl
import io
import gzip
from contextlib import asynccontextmanager
from datetime import datetime, timedelta, timezone
from collections.abc import Generator
from collections.abc import Callable
from types_aiobotocore_s3.client import S3Client
from src.shared import Logger
from collections import defaultdict
from botocore.exceptions import ClientError
import json
from typing import Any

import grpc
import aioboto3

from gametime_protos.mlp.prism.v1.service_pb2 import (
    OfflineFeaturesServiceCreateDatasetRequest,
    OfflineFeaturesServiceCreateDatasetResponse,
    OfflineFeaturesServiceDescribeDatasetRequest,
    OfflineFeaturesServiceDescribeDatasetResponse,
    OfflineFeaturesServiceFetchDatasetRequest,
    OfflineFeaturesServiceFetchDatasetResponse,
    PresignedUrl,
    FeatureRequest as FeatureRequestProto,
    Window as WindowProto,
    DatasetStatus,
)
from gametime_protos.mlp.prism.v1.service_pb2_grpc import (
    OfflineFeaturesServiceServicer,
    add_OfflineFeaturesServiceServicer_to_server,
)

from src.families import Registry, Family, FamilyNotFoundError
from src.stores import OnlineStore, OfflineStore, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
from src.aggregations import (
    AggregationFunction,
    REGISTRY as AGGREGATION_REGISTRY,
    Aggregation,
    AggregationDescriptor,
)
from src.config import config
from src.shared import get_logger, log_call
from src.utils import utcnow, calculate_partition
from src.activities import ensure_utc

register_servicer = add_OfflineFeaturesServiceServicer_to_server
logger = get_logger(__name__)


def parse_window_proto(window_proto: WindowProto | None) -> timedelta | None:
    if not window_proto:
        return None
    delta = timedelta(
        weeks=window_proto.weeks,
        days=window_proto.days,
        hours=window_proto.hours,
        minutes=window_proto.minutes,
        seconds=window_proto.seconds,
        milliseconds=window_proto.milliseconds,
        microseconds=window_proto.microseconds,
    )
    return delta if delta.total_seconds() > 0 else None


def find_row_at_or_before_polars(
    df: pl.DataFrame, target_ts: datetime, ts_col: str
) -> pl.DataFrame | None:
    """
    Finds the row with the latest timestamp <= target_ts in a Polars DataFrame.
    Assumes the DataFrame is sorted by timestamp.

    Args:
        df: Polars DataFrame, sorted ascending by ts_col.
        target_ts: The target timestamp (timezone-aware UTC).
        ts_col: Name of the timestamp column.

    Returns:
        A Polars DataFrame containing the single matching row, or None.
    """
    find_logger = logger.with_context(target_ts=target_ts.isoformat(), ts_col=ts_col)
    # TODO: make sure static analysis is done so we can remove null check
    if df is None or df.is_empty() or ts_col not in df.columns:
        find_logger.debug(f"Input DataFrame empty or missing '{ts_col}' column.")
        return None

    target_ts_utc = ensure_utc(target_ts)
    if target_ts_utc is None:
        find_logger.error(f"Could not convert target timestamp {target_ts} to UTC.")
        return None

    df_ts_dtype = df[ts_col].dtype
    expected_dtype = pl.Datetime(time_unit="us", time_zone="UTC")

    needs_conversion = (
        not isinstance(df_ts_dtype, pl.Datetime)
        or df_ts_dtype.time_unit != "us"
        or df_ts_dtype.time_zone != "UTC"
    )

    normalized_df = df
    if needs_conversion:
        find_logger.debug(
            f"Converting DataFrame '{ts_col}' (current: {df_ts_dtype}) to {expected_dtype} for comparison."
        )
        try:
            normalized_df = df.with_columns(
                pl.col(ts_col)
                .map_elements(ensure_utc, return_dtype=expected_dtype, skip_nulls=False)
                .alias(ts_col)
            ).filter(pl.col(ts_col).is_not_null())

            if normalized_df.is_empty():
                find_logger.debug(
                    f"DataFrame empty after timestamp conversion/filtering for {target_ts_utc}."
                )
                return None
        except Exception as e:
            find_logger.error(
                f"Error converting timestamp column '{ts_col}' to {expected_dtype}: {e}",
                exc_info=True,
            )
            return None

    try:
        # TODO: ensure we want = and add comment for reasoning
        filtered_df = normalized_df.filter(pl.col(ts_col) <= target_ts_utc)
    except Exception as filter_e:
        find_logger.error(
            f"Error filtering DataFrame by timestamp: {filter_e}", exc_info=True
        )
        return None

    if filtered_df.is_empty():
        min_ts = normalized_df[ts_col].min() if not normalized_df.is_empty() else "N/A"
        max_ts = normalized_df[ts_col].max() if not normalized_df.is_empty() else "N/A"
        find_logger.debug(
            f"No rows found with timestamp <= {target_ts_utc}. Data range: {min_ts} to {max_ts}"
        )
        return None
    else:
        result_row = filtered_df.tail(1)
        find_logger.debug(
            f"Found row at or before {target_ts_utc}: ts={result_row[ts_col][0]}"
        )
        return result_row


def get_state_from_row(
    row_df: pl.DataFrame | None, state_key: str
) -> tuple[float, ...] | float | list | None:
    """Safely extracts and potentially parses a state value from a single-row DataFrame."""
    state_logger = logger.with_context(state_key=state_key)
    if row_df is None or row_df.is_empty():
        state_logger.debug("Input DataFrame is None or empty.")
        return None
    try:
        if state_key not in row_df.columns:
            state_logger.warning(
                f"State key '{state_key}' not found in row DataFrame columns: {row_df.columns}"
            )
            return None

        value = row_df.select(state_key).item()

        if isinstance(value, (list, tuple)):
            try:
                return tuple(
                    float(v)
                    if isinstance(v, (int, float, str))
                    and v is not None
                    and str(v).replace(".", "", 1).isdigit()
                    else v
                    for v in value
                )
            except (ValueError, TypeError):
                state_logger.warning(
                    f"Could not convert elements of state list/tuple {value} to float."
                )
                return tuple(value)
        elif isinstance(value, str):
            state_logger.debug(
                f"State value for '{state_key}' is a string, attempting JSON parse."
            )
            try:
                parsed_value = json.loads(value)
                if isinstance(parsed_value, (list, tuple)):
                    try:
                        return tuple(
                            float(v)
                            if isinstance(v, (int, float, str))
                            and v is not None
                            and str(v).replace(".", "", 1).isdigit()
                            else v
                            for v in parsed_value
                        )
                    except (ValueError, TypeError):
                        state_logger.warning(
                            f"Could not convert elements of parsed state {parsed_value} to float."
                        )
                        return tuple(parsed_value)
                else:
                    state_logger.warning(
                        f"Parsed state JSON for '{state_key}' is not a list/tuple: {type(parsed_value)}"
                    )
                    return None
            except json.JSONDecodeError:
                state_logger.warning(f"Failed to JSON decode state string: '{value}'")
                return None
        elif value is None:
            state_logger.debug(f"State value for '{state_key}' is None.")
            return None
        else:
            state_logger.debug(
                f"State value for '{state_key}' is a primitive: {value} ({type(value)}). Aggregation might store primitives directly."
            )
            return value

    except pl.ColumnNotFoundError:
        state_logger.warning(f"State key '{state_key}' definitely not found in row.")
        return None
    except Exception as e:
        state_logger.error(
            f"Unexpected error getting state '{state_key}' from row: {e}", exc_info=True
        )
        return None


# TODO: consider renaming Offline/Online to something more aligned
class Servicer(OfflineFeaturesServiceServicer):
    def __init__(
        self, registry: Registry, online_store: OnlineStore, offline_store: OfflineStore
    ):
        self.registry = registry
        self.online_store = online_store
        self.offline_store = offline_store
        self._session = aioboto3.Session()
        self.s3_config = config.s3.settings
        self.output_bucket = config.s3.bucket
        logger.info("OfflineFeaturesServicer initialized.")

    @asynccontextmanager
    async def _get_s3_client(self) -> Generator[S3Client, None, None]:
        """Provides an S3 client context."""
        client_kwargs = {}
        if self.s3_config.get("endpoint_url"):
            client_kwargs["endpoint_url"] = self.s3_config["endpoint_url"]
        if self.s3_config.get("aws_access_key_id"):
            client_kwargs["aws_access_key_id"] = self.s3_config["aws_access_key_id"]
        if self.s3_config.get("aws_secret_access_key"):
            client_kwargs["aws_secret_access_key"] = self.s3_config[
                "aws_secret_access_key"
            ]

        async with self._session.client("s3", **client_kwargs) as client:
            yield client

    def _get_output_s3_key(self, dataset_ref: str, chunk_index: int = 0) -> str:
        return f"offline_datasets/{dataset_ref}/results_{chunk_index:04d}.csv.gz"

    def _find_aggregation_descriptor(
        self,
        family: Family,
        feat_req_proto: FeatureRequestProto,
        aggregation_impl: type[Aggregation],
        req_key: str,
        req_logger: Logger,
    ) -> AggregationDescriptor | None:
        """Finds the matching AggregationDescriptor."""
        if not feat_req_proto.column:
            req_logger.error(
                f"Missing column name in feature request '{req_key}' for family '{family.name}'."
            )
            return None

        descriptors = family.aggregation_descriptors
        if not descriptors:
            req_logger.error(
                f"No aggregation descriptors found for family '{family.name}'. Check family configuration."
            )
            return None

        descriptor = next(
            (
                d
                for d in descriptors
                if d.column == feat_req_proto.column
                and d.aggregation == aggregation_impl
            ),
            None,
        )
        if not descriptor:
            req_logger.error(
                f"Aggregation descriptor not found for column '{feat_req_proto.column}' "
                f"and aggregation type '{aggregation_impl.SHORT_NAME}' in family '{family.name}'. "
                f"Check if feature '{feat_req_proto.column}' with aggregation '{aggregation_impl.SHORT_NAME}' "
                f"is defined in the family config. Request key: '{req_key}'"
            )
        return descriptor

    async def _read_spine_from_s3(
        self, s3_uri: str, req_logger: Logger
    ) -> list[dict[str, Any]]:
        """Reads spine data from an S3 URI (single file or prefix)."""
        spine_points = []
        if not s3_uri.startswith("s3://"):
            req_logger.error(f"Invalid S3 URI for spine: {s3_uri}")
            return []

        parsed_s3_uri = s3_uri.replace("s3://", "").split("/", 1)
        spine_bucket = parsed_s3_uri[0]
        spine_key_or_prefix = parsed_s3_uri[1] if len(parsed_s3_uri) > 1 else ""

        async with self._get_s3_client() as s3_client:
            keys_to_process = []
            if spine_key_or_prefix.endswith((".csv", ".csv.gz")):
                try:
                    await s3_client.head_object(
                        Bucket=spine_bucket, Key=spine_key_or_prefix
                    )
                    keys_to_process.append(spine_key_or_prefix)
                except ClientError as e:
                    if e.response["Error"]["Code"] in ("404", "NoSuchKey"):
                        req_logger.error(f"Spine S3 object not found: {s3_uri}")
                        return []
                    else:
                        req_logger.error(
                            f"Error checking spine S3 object {s3_uri}: {e}"
                        )
                        return []
            else:
                try:
                    paginator = s3_client.get_paginator("list_objects_v2")
                    async for page in paginator.paginate(
                        Bucket=spine_bucket, Prefix=spine_key_or_prefix
                    ):
                        for obj in page.get("Contents", []):
                            if obj["Key"].endswith((".csv", ".csv.gz")):
                                keys_to_process.append(obj["Key"])
                except Exception as e:
                    req_logger.error(
                        f"Error listing spine S3 objects for prefix {s3_uri}: {e}"
                    )
                    return []

            if not keys_to_process:
                req_logger.warning(f"No CSV or CSV.GZ files found at S3 URI: {s3_uri}")
                return []

            for s3_key in keys_to_process:
                req_logger.info(f"Reading spine data from s3://{spine_bucket}/{s3_key}")
                try:
                    response = await s3_client.get_object(
                        Bucket=spine_bucket, Key=s3_key
                    )
                    async with response["Body"] as stream:
                        content_bytes = await stream.read()

                    data_buffer = io.BytesIO(content_bytes)
                    if s3_key.endswith(".gz"):
                        spine_df = pl.read_csv(
                            data_buffer, compression="gzip", try_parse_dates=False
                        )
                    else:
                        spine_df = pl.read_csv(data_buffer, try_parse_dates=False)

                    if "timestamp" not in spine_df.columns:
                        req_logger.error(
                            f"'timestamp' column missing in spine file: {s3_key}"
                        )
                        continue

                    spine_df = spine_df.with_columns(
                        pl.col("timestamp")
                        .str.to_datetime(
                            format="%Y-%m-%dT%H:%M:%S%.fZ", strict=False, exact=False
                        )
                        .dt.replace_time_zone("UTC")
                        .alias("timestamp")
                    ).filter(pl.col("timestamp").is_not_null())

                    for row in spine_df.iter_rows(named=True):
                        timestamp = row.pop("timestamp", None)
                        if not isinstance(timestamp, datetime):
                            req_logger.warning(
                                f"Skipping row due to invalid timestamp: {timestamp} in {s3_key}"
                            )
                            continue

                        entity_identifiers = {k: str(v) for k, v in row.items()}

                        spine_points.append(
                            {
                                "entity_identifiers": entity_identifiers,
                                "timestamp": timestamp,
                            }
                        )
                except Exception as e:
                    req_logger.error(
                        f"Failed to read or parse spine file {s3_key}: {e}",
                        exc_info=True,
                    )
                    continue

        req_logger.info(
            f"Successfully read {len(spine_points)} spine points from {s3_uri}"
        )
        return spine_points

    @log_call(logger=logger)
    async def CreateDataset(
        self, request: OfflineFeaturesServiceCreateDatasetRequest, context
    ) -> OfflineFeaturesServiceCreateDatasetResponse:
        req_logger = logger.with_context(
            request_id=getattr(context, "request_id", "N/A")
        )
        req_logger.info(
            f"Received CreateDataset request with {len(request.feature_requests)} feature requests."
        )

        if not request.feature_requests:
            await context.abort(
                grpc.StatusCode.INVALID_ARGUMENT,
                "feature_requests map cannot be empty.",
            )
            return OfflineFeaturesServiceCreateDatasetResponse()

        dataset_ref = f"prism-ds-{uuid.uuid4()}"
        req_logger = req_logger.with_context(dataset_ref=dataset_ref)
        req_logger.info("Generated dataset reference.")

        spine_data: list[dict[str, Any]] = []
        if request.spine_s3_uri:
            req_logger.info(f"Reading spine data from S3 URI: {request.spine_s3_uri}")
            spine_data = await self._read_spine_from_s3(
                request.spine_s3_uri, req_logger
            )
            if not spine_data:
                await context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    f"Could not read or parse spine data from {request.spine_s3_uri}. Check logs for details.",
                )
                return OfflineFeaturesServiceCreateDatasetResponse()
        else:
            req_logger.warning(
                "spine_s3_uri not provided. Using simulated spine data. This should be updated."
            )

            spine_data = [
                {
                    "entity_identifiers": {"user_id": "u1"},
                    "timestamp": datetime(2024, 1, 10, 15, 0, 0, tzinfo=timezone.utc),
                },
                {
                    "entity_identifiers": {"user_id": "u1"},
                    "timestamp": datetime(2024, 1, 12, 11, 0, 0, tzinfo=timezone.utc),
                },
                {
                    "entity_identifiers": {"user_id": "u2"},
                    "timestamp": datetime(2024, 1, 10, 15, 0, 0, tzinfo=timezone.utc),
                },
            ]

        if not spine_data:
            await context.abort(
                grpc.StatusCode.INVALID_ARGUMENT,
                "Spine data is empty. Cannot create dataset.",
            )
            return OfflineFeaturesServiceCreateDatasetResponse()

        req_logger.info(f"Processing {len(spine_data)} spine entries.")

        entity_requests: dict[str, dict[str, Any]] = defaultdict(
            lambda: {"min_ts_needed": None, "max_ts_needed": None, "spine_points": []}
        )
        all_requested_features: dict[str, FeatureRequestProto] = (
            request.feature_requests
        )
        involved_families = set()
        min_global_window_start: datetime | None = None

        latest_spine_ts = (
            max(
                sp["timestamp"]
                for sp in spine_data
                if "timestamp" in sp and isinstance(sp["timestamp"], datetime)
            )
            if spine_data
            else utcnow()
        )

        for feat_req in all_requested_features.values():
            involved_families.add(feat_req.family)
            window = parse_window_proto(feat_req.aggregation.window)
            if window:
                window_start = latest_spine_ts - window
                if (
                    min_global_window_start is None
                    or window_start < min_global_window_start
                ):
                    min_global_window_start = window_start

        for spine_entry in spine_data:
            entity_id_map = spine_entry.get("entity_identifiers")
            timestamp = spine_entry.get("timestamp")

            if not entity_id_map or not isinstance(entity_id_map, dict):
                req_logger.warning(
                    f"Skipping spine entry due to missing or invalid entity_identifiers: {spine_entry}"
                )
                continue
            if not timestamp or not isinstance(timestamp, datetime):
                req_logger.warning(
                    f"Skipping spine entry due to missing or invalid timestamp: {spine_entry}"
                )
                continue

            timestamp = ensure_utc(timestamp)
            if timestamp is None:
                req_logger.warning(
                    f"Skipping spine entry due to invalid timestamp after ensure_utc: {spine_entry}"
                )
                continue

            entity_key_parts = [f"{k}={v}" for k, v in sorted(entity_id_map.items())]
            entity_key = "|".join(entity_key_parts)

            entity_data = entity_requests[entity_key]

            entity_earliest_needed = timestamp
            if min_global_window_start and min_global_window_start < timestamp:
                entity_earliest_needed = min(min_global_window_start, timestamp)

            if (
                entity_data["min_ts_needed"] is None
                or entity_earliest_needed < entity_data["min_ts_needed"]
            ):
                entity_data["min_ts_needed"] = entity_earliest_needed
            if (
                entity_data["max_ts_needed"] is None
                or timestamp > entity_data["max_ts_needed"]
            ):
                entity_data["max_ts_needed"] = timestamp

            entity_data["spine_points"].append(
                {
                    "timestamp": timestamp,
                    "entity_identifiers": entity_id_map,
                    "features_needed": list(all_requested_features.keys()),
                }
            )

        results_list: list[dict[str, str | datetime | float | None]] = []
        family_cache: dict[str, Family] = {}

        for entity_key, entity_data in entity_requests.items():
            entity_min_ts_needed = entity_data["min_ts_needed"]
            entity_max_ts_needed = entity_data["max_ts_needed"]
            entity_spine_points = entity_data["spine_points"]

            if (
                not entity_spine_points
                or entity_min_ts_needed is None
                or entity_max_ts_needed is None
            ):
                req_logger.warning(
                    f"Skipping entity {entity_key} due to missing spine points or time range."
                )
                continue

            current_entity_identifiers_map = entity_spine_points[0][
                "entity_identifiers"
            ]

            entity_logger = req_logger.with_context(entity=entity_key)
            entity_logger.info(
                f"Processing entity. Required Time Range: {entity_min_ts_needed} to {entity_max_ts_needed}"
            )

            family_dataframes: dict[str, pl.DataFrame | None] = {}

            for family_name in involved_families:
                family_logger = entity_logger.with_context(family=family_name)
                family_logger.debug(f"Fetching data for family '{family_name}'.")

                try:
                    if family_name not in family_cache:
                        family = await self.registry.fetch_one(family_name)
                        family_cache[family_name] = family
                    else:
                        family = family_cache[family_name]

                    ts_col = family.config.timestamp_column
                    id_col = family.config.id_column

                    try:
                        missing_ids_for_family = [
                            id_name
                            for id_name in family.config.identifier_columns
                            if id_name not in current_entity_identifiers_map
                        ]
                        if missing_ids_for_family:
                            family_logger.warning(
                                f"Spine data for entity {entity_key} is missing required identifiers {missing_ids_for_family} for family {family.name}. Skipping family fetch."
                            )
                            family_dataframes[family_name] = pl.DataFrame()
                            continue

                        packed_identifiers = family.pack_identifiers(
                            current_entity_identifiers_map
                        )
                    except KeyError as e:
                        family_logger.warning(
                            f"Missing identifier '{e}' needed by family {family.name} for entity {entity_key} (from spine). Skipping family fetch."
                        )
                        family_dataframes[family_name] = pl.DataFrame()
                        continue

                    family_logger.debug(
                        f"Fetching recent rows from Redis for packed ID: {packed_identifiers}"
                    )
                    redis_rows_dicts = await self.online_store.read_recent_rows(
                        family,
                        packed_identifiers,
                        num_rows=MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS * 2,
                    )
                    family_logger.debug(f"Got {len(redis_rows_dicts)} rows from Redis.")
                    redis_df = (
                        pl.DataFrame(redis_rows_dicts, strict=False)
                        if redis_rows_dicts
                        else pl.DataFrame()
                    )

                    s3_combined_df = pl.DataFrame()
                    entity_partition_num = -1
                    if family.pipeline_settings:
                        entity_partition_num = calculate_partition(
                            packed_identifiers, family.pipeline_settings.num_partitions
                        )
                        family_logger.debug(
                            f"Entity '{packed_identifiers}' belongs to partition {entity_partition_num}"
                        )

                        s3_start_date = entity_min_ts_needed.date()
                        s3_end_date = entity_max_ts_needed.date()

                        family_logger.debug(
                            f"Fetching S3 data for partition {entity_partition_num} between {s3_start_date} and {s3_end_date}"
                        )
                        s3_combined_df_optional = (
                            await self.offline_store.read_specific_partition_in_range(
                                family, entity_partition_num, s3_start_date, s3_end_date
                            )
                        )
                        if s3_combined_df_optional is not None:
                            s3_combined_df = s3_combined_df_optional
                            family_logger.info(
                                f"Read {len(s3_combined_df)} total rows from S3 partition {entity_partition_num}."
                            )
                        else:
                            family_logger.warning(
                                f"Failed to read data from S3 partition {entity_partition_num}. Proceeding without S3 data."
                            )
                            s3_combined_df = pl.DataFrame()

                    else:
                        family_logger.error(
                            f"Family {family.name} missing pipeline_settings. Cannot determine S3 partition."
                        )

                    combined_df: pl.DataFrame
                    if not redis_df.is_empty() and not s3_combined_df.is_empty():
                        try:
                            s3_cols = set(s3_combined_df.columns)
                            redis_cols = set(redis_df.columns)
                            all_cols = s3_cols.union(redis_cols)

                            if s3_cols != all_cols:
                                for col_name in all_cols - s3_cols:
                                    col_type = (
                                        redis_df[col_name].dtype
                                        if col_name in redis_cols
                                        else pl.Utf8
                                    )
                                    s3_combined_df = s3_combined_df.with_columns(
                                        pl.lit(None, dtype=col_type).alias(col_name)
                                    )
                            if redis_cols != all_cols:
                                for col_name in all_cols - redis_cols:
                                    col_type = (
                                        s3_combined_df[col_name].dtype
                                        if col_name in s3_cols
                                        else pl.Utf8
                                    )
                                    redis_df = redis_df.with_columns(
                                        pl.lit(None, dtype=col_type).alias(col_name)
                                    )

                            s3_combined_df = s3_combined_df.select(
                                sorted(list(all_cols))
                            )
                            redis_df = redis_df.select(sorted(list(all_cols)))

                            combined_df = pl.concat(
                                [s3_combined_df, redis_df], how="vertical_relaxed"
                            )
                            family_logger.debug(
                                f"Combined Redis ({len(redis_df)}) and S3 ({len(s3_combined_df)}) data. Total: {len(combined_df)}"
                            )
                        except Exception as concat_err:
                            family_logger.error(
                                f"Error concatenating Redis and S3 data: {concat_err}. Using only Redis data.",
                                exc_info=True,
                            )
                            combined_df = redis_df
                    elif not redis_df.is_empty():
                        combined_df = redis_df
                        family_logger.debug("Using only Redis data.")
                    elif not s3_combined_df.is_empty():
                        combined_df = s3_combined_df
                        family_logger.debug("Using only S3 data.")
                    else:
                        combined_df = pl.DataFrame()
                        family_logger.warning(
                            "No combined data found (neither Redis nor S3)."
                        )

                    if not combined_df.is_empty():
                        if ts_col not in combined_df.columns:
                            family_logger.error(
                                f"Timestamp column '{ts_col}' missing from combined data for family {family.name}. Cannot process."
                            )
                            family_dataframes[family_name] = pl.DataFrame()
                            continue

                        try:
                            if not isinstance(combined_df[ts_col].dtype, pl.Datetime):
                                family_logger.warning(
                                    f"Combined data timestamp column '{ts_col}' is not Datetime ({combined_df[ts_col].dtype}). Attempting conversion."
                                )
                                combined_df = combined_df.with_columns(
                                    pl.col(ts_col)
                                    .map_elements(
                                        ensure_utc,
                                        return_dtype=pl.Datetime(
                                            time_unit="us", time_zone="UTC"
                                        ),
                                        skip_nulls=False,
                                    )
                                    .alias(ts_col)
                                ).filter(pl.col(ts_col).is_not_null())

                            if combined_df.is_empty():
                                family_logger.warning(
                                    "Data became empty after timestamp conversion/filtering."
                                )
                                family_dataframes[family_name] = pl.DataFrame()
                                continue

                            filter_exprs = []
                            for id_col_name in family.config.identifier_columns:
                                if (
                                    id_col_name in combined_df.columns
                                    and id_col_name in current_entity_identifiers_map
                                ):
                                    filter_exprs.append(
                                        pl.col(id_col_name).cast(pl.Utf8)
                                        == str(
                                            current_entity_identifiers_map[id_col_name]
                                        )
                                    )
                                else:
                                    family_logger.warning(
                                        f"Identifier column {id_col_name} not found in combined_df or current_entity_identifiers_map for filtering."
                                    )
                                    filter_exprs.append(pl.lit(False))
                                    break

                            if filter_exprs:
                                combined_filter = filter_exprs[0]
                                for i in range(1, len(filter_exprs)):
                                    combined_filter = combined_filter & filter_exprs[i]
                                combined_df = combined_df.filter(combined_filter)

                            if combined_df.is_empty():
                                family_logger.info(
                                    f"No data for entity {entity_key} in family {family.name} after filtering."
                                )
                                family_dataframes[family_name] = pl.DataFrame()
                                continue

                            combined_df = combined_df.filter(
                                (pl.col(ts_col) >= entity_min_ts_needed)
                                & (pl.col(ts_col) <= entity_max_ts_needed)
                            )

                            if id_col in combined_df.columns:
                                family_logger.debug(
                                    f"Deduplicating combined data based on '{id_col}', keeping latest '{ts_col}'."
                                )

                                combined_df = combined_df.sort(
                                    ts_col, id_col, descending=[True, False]
                                ).unique(
                                    subset=[id_col], keep="first", maintain_order=False
                                )

                            sorted_df = combined_df.sort(ts_col)
                            family_dataframes[family_name] = sorted_df
                            family_logger.info(
                                f"Prepared sorted DataFrame with {len(sorted_df)} rows for family '{family.name}' and entity '{entity_key}'."
                            )

                        except Exception as process_err:
                            family_logger.error(
                                f"Error processing combined data (type conversion, sort, unique): {process_err}",
                                exc_info=True,
                            )
                            family_dataframes[family_name] = pl.DataFrame()
                    else:
                        family_dataframes[family_name] = pl.DataFrame()

                except FamilyNotFoundError:
                    entity_logger.warning(
                        f"Family '{family_name}' not found. Skipping."
                    )
                    family_dataframes[family_name] = pl.DataFrame()
                except Exception as e:
                    entity_logger.error(
                        f"Error processing data for family {family_name} for entity {entity_key}: {e}",
                        exc_info=True,
                    )
                    family_dataframes[family_name] = pl.DataFrame()

            for spine_point in entity_spine_points:
                target_ts = spine_point["timestamp"]

                spine_identifiers_map = spine_point["entity_identifiers"]
                spine_logger = entity_logger.with_context(
                    timestamp=target_ts.isoformat()
                )
                spine_logger.info("Computing features for spine point.")

                result_row_features = {}

                for req_key, feat_req_proto in all_requested_features.items():
                    family_name = feat_req_proto.family
                    feature_logger = spine_logger.with_context(
                        feature_req=req_key, family=family_name
                    )

                    entity_family_df = family_dataframes.get(family_name)

                    if entity_family_df is None:
                        feature_logger.error(
                            f"Data fetching failed previously for family '{family_name}'. Result is null."
                        )
                        result_row_features[req_key] = None
                        continue
                    if entity_family_df.is_empty():
                        feature_logger.debug(
                            f"No historical data available for family '{family_name}'. Result is null."
                        )
                        result_row_features[req_key] = None
                        continue

                    family = family_cache[family_name]
                    ts_col = family.config.timestamp_column

                    try:
                        agg_func_enum_proto = feat_req_proto.aggregation.function
                        internal_agg_enum = AggregationFunction(agg_func_enum_proto)
                        aggregation_impl = AGGREGATION_REGISTRY.get(internal_agg_enum)
                        if not aggregation_impl:
                            raise ValueError(
                                f"Unsupported aggregation function enum value: {agg_func_enum_proto}"
                            )
                    except (ValueError, KeyError) as e:
                        feature_logger.error(
                            f"Invalid/unsupported aggregation function in request: {e}. Result is null."
                        )
                        result_row_features[req_key] = None
                        continue

                    descriptor = self._find_aggregation_descriptor(
                        family,
                        feat_req_proto,
                        aggregation_impl,
                        req_key,
                        feature_logger,
                    )
                    if not descriptor:
                        result_row_features[req_key] = None
                        continue

                    window = parse_window_proto(feat_req_proto.aggregation.window)
                    computed_value = None

                    end_row_df = find_row_at_or_before_polars(
                        entity_family_df, target_ts, ts_col
                    )

                    if end_row_df is None:
                        feature_logger.debug(
                            f"No data found at or before target_ts {target_ts}. Result is null."
                        )
                        computed_value = None
                    else:
                        end_state = get_state_from_row(end_row_df, descriptor.state_key)

                        if window:
                            start_ts = target_ts - window
                            feature_logger.debug(
                                f"Windowed feature: target={target_ts}, window={window}, start={start_ts}"
                            )
                            start_row_df = find_row_at_or_before_polars(
                                entity_family_df, start_ts, ts_col
                            )
                            start_state = (
                                get_state_from_row(start_row_df, descriptor.state_key)
                                if start_row_df is not None
                                else None
                            )

                            if start_state is None and start_row_df is not None:
                                feature_logger.warning(
                                    f"Found start row at {start_row_df[ts_col][0]} but state key '{descriptor.state_key}' was missing/null."
                                )
                            elif start_state is None:
                                feature_logger.debug(
                                    f"No row found at or before start_ts {start_ts}, using initial state for start."
                                )

                            try:
                                prepared_states = self._prepare_arrow_state(
                                    aggregation_impl.compute_windowed,
                                    start_state,
                                    end_state,
                                    is_windowed=True,
                                )
                                if prepared_states == (None, None):
                                    raise ValueError(
                                        "Failed to prepare Arrow states for windowed computation."
                                    )

                                start_components, end_components = prepared_states
                                feature_logger.debug(
                                    f"Calling compute_windowed with start_state: {start_state}, end_state: {end_state}"
                                )

                                pa_result = aggregation_impl.compute_windowed(
                                    *start_components, *end_components
                                )

                                if (
                                    pa_result is not None
                                    and len(pa_result) > 0
                                    and not pa_result[0].is_null
                                ):
                                    computed_value = pa_result[0].as_py()
                                    feature_logger.debug(
                                        f"Computed windowed value: {computed_value}"
                                    )
                                else:
                                    feature_logger.debug(
                                        "Windowed computation result was null or empty."
                                    )
                                    computed_value = None
                            except Exception as compute_err:
                                feature_logger.error(
                                    f"Error during compute_windowed: {compute_err}",
                                    exc_info=True,
                                )
                                computed_value = None
                        else:
                            feature_logger.debug(
                                f"Point-in-time feature at {target_ts}"
                            )
                            if end_state is None:
                                feature_logger.warning(
                                    f"End row found but state key '{descriptor.state_key}' was missing/null."
                                )
                                computed_value = None
                            else:
                                try:
                                    prepared_states = self._prepare_arrow_state(
                                        aggregation_impl.compute,
                                        end_state,
                                        is_windowed=False,
                                    )
                                    if prepared_states == (None,):
                                        raise ValueError(
                                            "Failed to prepare Arrow states for point-in-time computation."
                                        )

                                    state_components = prepared_states[0]
                                    feature_logger.debug(
                                        f"Calling compute with state: {end_state}"
                                    )

                                    pa_result = aggregation_impl.compute(
                                        *state_components
                                    )

                                    if pa_result is not None and len(pa_result) > 0:
                                        scalar_value = pa_result[0]
                                        if hasattr(scalar_value, "is_null"):
                                            if not scalar_value.is_null:
                                                computed_value = scalar_value.as_py()
                                            else:
                                                computed_value = None
                                        else:
                                            computed_value = (
                                                scalar_value.as_py()
                                                if scalar_value is not None
                                                else None
                                            )
                                        feature_logger.debug(
                                            f"Computed point-in-time value: {computed_value}"
                                        )
                                    else:
                                        feature_logger.debug(
                                            "Point-in-time computation result was null or empty."
                                        )
                                        computed_value = None
                                except Exception as compute_err:
                                    feature_logger.error(
                                        f"Error during compute: {compute_err}",
                                        exc_info=True,
                                    )
                                    computed_value = None

                    result_row_features[req_key] = computed_value

                result_row_dict = {
                    **spine_identifiers_map,
                    "timestamp": target_ts,
                    **result_row_features,
                }
                results_list.append(result_row_dict)

        if not results_list:
            req_logger.warning(
                "No features were computed for any spine point. Returning empty dataset reference."
            )
            output_key = self._get_output_s3_key(dataset_ref)
            empty_df = pl.DataFrame()
            compressed_buffer = io.BytesIO()

            csv_buffer = io.StringIO()
            empty_df.write_csv(csv_buffer)

            csv_buffer.seek(0)
            with gzip.GzipFile(fileobj=compressed_buffer, mode="wb") as gz:
                gz.write(csv_buffer.getvalue().encode("utf-8"))
            compressed_buffer.seek(0)
            try:
                async with self._get_s3_client() as s3_client:
                    await s3_client.put_object(
                        Bucket=self.output_bucket,
                        Key=output_key,
                        Body=compressed_buffer,
                    )
                req_logger.info(
                    f"Created empty dataset file at s3://{self.output_bucket}/{output_key}"
                )
            except Exception as put_err:
                req_logger.error(
                    f"Failed to upload empty dataset file: {put_err}", exc_info=True
                )
                await context.abort(
                    grpc.StatusCode.INTERNAL, "Failed to generate empty dataset file."
                )
                return OfflineFeaturesServiceCreateDatasetResponse()

            return OfflineFeaturesServiceCreateDatasetResponse(
                ref=dataset_ref, presigned_urls=[]
            )

        req_logger.info(
            f"Formatting {len(results_list)} result rows into Polars DataFrame for CSV output."
        )
        try:
            results_df = pl.DataFrame(results_list, strict=False)

            first_spine_point_identifiers = results_list[0]

            id_cols_from_spine = sorted(
                [
                    k
                    for k in first_spine_point_identifiers
                    if k != "timestamp" and k not in all_requested_features
                ]
            )

            feature_cols = sorted(list(all_requested_features.keys()))
            ordered_cols = id_cols_from_spine + ["timestamp"] + feature_cols

            select_exprs = []
            for col in ordered_cols:
                if col in results_df.columns:
                    if col == "timestamp":
                        select_exprs.append(
                            pl.col(col).cast(
                                pl.Datetime(time_unit="us", time_zone="UTC")
                            )
                        )
                    elif (
                        results_df[col].dtype == pl.Float64
                        or results_df[col].dtype == pl.Float32
                    ):
                        select_exprs.append(pl.col(col).cast(pl.Float64))
                    elif results_df[col].dtype.is_integer():
                        select_exprs.append(pl.col(col).cast(pl.Int64))
                    elif (
                        isinstance(results_df[col].dtype, pl.String)
                        or results_df[col].dtype == pl.Utf8
                    ):
                        select_exprs.append(pl.col(col).cast(pl.Utf8))
                    else:
                        select_exprs.append(pl.col(col))
                else:
                    select_exprs.append(pl.lit(None).alias(col))

            results_df_ordered = results_df.select(select_exprs)

            output_key = self._get_output_s3_key(dataset_ref)
            req_logger.info(
                f"Writing dataset CSV using Polars to s3://{self.output_bucket}/{output_key}"
            )

            compressed_buffer = io.BytesIO()
            csv_buffer = io.StringIO()
            results_df_ordered.write_csv(
                csv_buffer,
                datetime_format="%Y-%m-%dT%H:%M:%S.%fZ",
                float_precision=8,
                null_value="",
                quote_style="necessary",
            )
            csv_buffer.seek(0)
            with gzip.GzipFile(fileobj=compressed_buffer, mode="wb") as gz:
                gz.write(csv_buffer.getvalue().encode("utf-8"))
            compressed_buffer.seek(0)

            async with self._get_s3_client() as s3_client:
                await s3_client.put_object(
                    Bucket=self.output_bucket, Key=output_key, Body=compressed_buffer
                )
            req_logger.info(f"Successfully uploaded dataset to {output_key}")

        except Exception as e:
            req_logger.error(
                f"Failed to format or upload results CSV using Polars: {e}",
                exc_info=True,
            )
            await context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to generate dataset file: {e}"
            )
            return OfflineFeaturesServiceCreateDatasetResponse()

        output_urls = []
        try:
            async with self._get_s3_client() as s3_client:
                url = await s3_client.generate_presigned_url(
                    "get_object",
                    Params={"Bucket": self.output_bucket, "Key": output_key},
                    ExpiresIn=3600,
                )
                output_urls.append(PresignedUrl(url=url))
                req_logger.info(f"Generated presigned URL for {output_key}")
        except Exception as e:
            req_logger.error(
                f"Failed to generate presigned URL for output: {e}", exc_info=True
            )

        req_logger.info("CreateDataset completed successfully.")
        return OfflineFeaturesServiceCreateDatasetResponse(
            ref=dataset_ref, presigned_urls=output_urls
        )

    def _prepare_arrow_state(
        self,
        compute_method: Callable,
        *state_values: tuple[float, ...] | float | list | None,
        is_windowed: bool = False,
    ) -> tuple[list[pa.Array] | None, ...] | tuple[None, ...]:
        """
        Prepares state values (which can be None, primitives, or lists/tuples)
        for PyArrow compute functions. Handles None inputs by creating pa.nulls arrays.
        """
        prepare_logger = logger.with_context(
            method_name=compute_method.__name__, is_windowed=is_windowed
        )

        if compute_method.__qualname__ == "SumAggregation.compute":
            num_expected_args = 2
        else:
            num_expected_args = compute_method.__code__.co_argcount - 1

        if is_windowed:
            num_expected_per_state = num_expected_args // 2
            if num_expected_args % 2 != 0:
                prepare_logger.error(
                    f"Windowed compute method has odd number of state args: {num_expected_args}"
                )
                return None, None

            start_state_val = state_values[0] if len(state_values) > 0 else None
            end_state_val = state_values[1] if len(state_values) > 1 else None
            prepare_logger.debug(
                f"Preparing windowed state. Start: {start_state_val}, End: {end_state_val}"
            )

            is_sum_aggregation = compute_method.__qualname__ == "SumAggregation.compute"

            start_components = self._arrowfy_single_state(
                start_state_val,
                num_expected_per_state,
                compute_method.__name__,
                "start",
                prepare_logger,
                is_sum_aggregation,
            )
            end_components = self._arrowfy_single_state(
                end_state_val,
                num_expected_per_state,
                compute_method.__name__,
                "end",
                prepare_logger,
                is_sum_aggregation,
            )

            if start_components is None or end_components is None:
                prepare_logger.error("Failed to arrowfy one or both window states.")
                return None, None

            return start_components, end_components
        else:
            state_val = state_values[0] if len(state_values) > 0 else None
            prepare_logger.debug(f"Preparing point-in-time state: {state_val}")

            is_sum_aggregation = compute_method.__qualname__ == "SumAggregation.compute"

            components = self._arrowfy_single_state(
                state_val,
                num_expected_args,
                compute_method.__name__,
                "state",
                prepare_logger,
                is_sum_aggregation,
            )

            if components is None:
                prepare_logger.error("Failed to arrowfy point-in-time state.")
                return (None,)

            return (components,)

    def _arrowfy_single_state(
        self,
        state_value: tuple[float, ...] | float | list | None,
        num_expected_components: int,
        method_name: str,
        state_desc: str,
        parent_logger: Logger,
        is_sum_aggregation: bool = False,
    ) -> list[pa.Array] | None:
        """Converts a single state value (primitive, tuple/list, or None) into a list of Arrow arrays."""
        state_logger = parent_logger.with_context(state_desc=state_desc)

        if state_value is None:
            state_logger.debug(
                f"State is None. Creating {num_expected_components} null array(s)."
            )
            if is_sum_aggregation and num_expected_components == 0:
                return [pa.nulls(1, type=pa.float64()), pa.array([0], type=pa.int64())]
            elif num_expected_components == 0:
                return []

            return [pa.nulls(1, type=pa.float64())] * num_expected_components

        components = []
        try:
            if (
                hasattr(state_value, "__class__")
                and state_value.__class__.__name__ == "Series"
            ):
                try:
                    if is_sum_aggregation and len(state_value) == 2:
                        state_logger.debug(
                            f"Handling Polars Series for sum aggregation: {state_value}"
                        )
                        return [
                            pa.array([float(state_value[0])], type=pa.float64()),
                            pa.array([int(state_value[1])], type=pa.int64()),
                        ]
                    elif num_expected_components == 0 and method_name == "compute":
                        state_logger.debug(
                            f"Handling Polars Series for count aggregation: {state_value}"
                        )
                        count_value = int(state_value[0]) if len(state_value) > 0 else 0
                        return [pa.array([count_value], type=pa.int64())]
                except Exception as e:
                    state_logger.error(
                        f"Error converting Polars Series for aggregation: {e}",
                        exc_info=True,
                    )
                    return None
            elif isinstance(state_value, (list, tuple)):
                if len(state_value) == num_expected_components:
                    arrow_components = []
                    for i, v in enumerate(state_value):
                        try:
                            if v is None:
                                arrow_components.append(pa.nulls(1, type=pa.float64()))
                            else:
                                try:
                                    arr = pa.array([float(v)], type=pa.float64())
                                except (ValueError, TypeError):
                                    state_logger.warning(
                                        f"Could not convert state component {i} ({v}, type {type(v)}) to float64 for {method_name}, using original type."
                                    )
                                    arr = pa.array([v])
                                arrow_components.append(arr)

                        except Exception as component_err:
                            state_logger.error(
                                f"Error converting state component {i} ({v}) to Arrow array for {method_name}: {component_err}",
                                exc_info=True,
                            )
                            return None

                    components = arrow_components
                    state_logger.debug(
                        f"Converted state tuple/list {state_value} to {len(components)} Arrow arrays for {method_name}."
                    )
                else:
                    state_logger.error(
                        f"State arity mismatch for {method_name}: expected {num_expected_components} components, "
                        f"got tuple/list of length {len(state_value)}: {state_value}"
                    )
                    return None
            elif num_expected_components == 1:
                try:
                    if (
                        hasattr(state_value, "__class__")
                        and state_value.__class__.__name__ == "Series"
                    ):
                        state_logger.debug(f"Handling Polars Series: {state_value}")
                        if len(state_value) == 1:
                            actual_value = state_value[0]
                            try:
                                arr = pa.array([float(actual_value)], type=pa.float64())
                            except (ValueError, TypeError):
                                state_logger.warning(
                                    f"Could not convert Series single value {actual_value} to float64 for {method_name}, using original type."
                                )
                                arr = pa.array([actual_value])
                        else:
                            state_logger.warning(
                                f"Multi-value Series detected with {len(state_value)} values. Using first value only."
                            )
                            try:
                                arr = pa.array(
                                    [float(state_value[0])], type=pa.float64()
                                )
                            except (ValueError, TypeError):
                                arr = pa.array([state_value[0]])
                    else:
                        try:
                            arr = pa.array([float(state_value)], type=pa.float64())
                        except (ValueError, TypeError):
                            state_logger.warning(
                                f"Could not convert single state value {state_value} (type {type(state_value)}) to float64 for {method_name}, using original type."
                            )
                            arr = pa.array([state_value])
                    components = [arr]
                    state_logger.debug(
                        f"Converted single state value {state_value} to Arrow array for {method_name}."
                    )
                except Exception as single_comp_err:
                    state_logger.error(
                        f"Error converting single state value {state_value} to Arrow array for {method_name}: {single_comp_err}",
                        exc_info=True,
                    )
                    return None

            else:
                state_logger.error(
                    f"State type/arity mismatch for {method_name}: expected {num_expected_components} components, "
                    f"got single value {state_value} ({type(state_value)})"
                )
                return None

        except Exception as arrow_err:
            state_logger.error(
                f"Failed to convert state value {state_value} to Arrow array(s) for {method_name}: {arrow_err}",
                exc_info=True,
            )
            return None

        return components

    async def DescribeDataset(
        self, request: OfflineFeaturesServiceDescribeDatasetRequest, context
    ):
        desc_logger = logger.with_context(dataset_ref=request.ref)
        desc_logger.info("Received DescribeDataset request.")

        output_key = self._get_output_s3_key(request.ref)
        status = DatasetStatus.DATASET_STATUS_UNKNOWN
        status_detail = ""
        try:
            async with self._get_s3_client() as s3_client:
                await s3_client.head_object(Bucket=self.output_bucket, Key=output_key)
                status = DatasetStatus.DATASET_STATUS_READY
                status_detail = (
                    f"Result file s3://{self.output_bucket}/{output_key} is available."
                )
                desc_logger.info("Dataset found. Status: READY")
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code")
            if error_code in ("404", "NoSuchKey", "NotFound"):
                # TODO: Add logic here to check if the dataset generation *is* in progress
                status = DatasetStatus.DATASET_STATUS_NOT_FOUND
                status_detail = (
                    f"Result file s3://{self.output_bucket}/{output_key} not found."
                )
                desc_logger.warning("Dataset file not found. Status: NOT_FOUND")
            else:
                desc_logger.error(
                    f"S3 error checking dataset status: {e}", exc_info=True
                )
                status = DatasetStatus.DATASET_STATUS_FAILED
                status_detail = f"Error checking S3 status: {e}"
        except Exception as e:
            desc_logger.error(
                f"Unexpected error checking dataset status: {e}", exc_info=True
            )
            status = DatasetStatus.DATASET_STATUS_UNKNOWN
            status_detail = f"Unexpected error: {e}"

        return OfflineFeaturesServiceDescribeDatasetResponse(
            status=status, status_detail=status_detail
        )

    async def FetchDataset(
        self, request: OfflineFeaturesServiceFetchDatasetRequest, context
    ):
        fetch_logger = logger.with_context(dataset_ref=request.ref)
        fetch_logger.info("Received FetchDataset request.")
        # TODO: Add support for fetching multiple chunks if datasets become very large
        output_key = self._get_output_s3_key(request.ref, chunk_index=0)
        urls = []
        try:
            async with self._get_s3_client() as s3_client:
                try:
                    await s3_client.head_object(
                        Bucket=self.output_bucket, Key=output_key
                    )
                    fetch_logger.info(
                        f"Dataset file s3://{self.output_bucket}/{output_key} confirmed to exist."
                    )
                except ClientError as e:
                    error_code = e.response.get("Error", {}).get("Code")
                    if error_code in ("404", "NoSuchKey", "NotFound"):
                        fetch_logger.error(
                            f"Dataset file not found: s3://{self.output_bucket}/{output_key}"
                        )
                        await context.abort(
                            grpc.StatusCode.NOT_FOUND,
                            f"Dataset ref '{request.ref}' not found or result file is missing.",
                        )
                        return OfflineFeaturesServiceFetchDatasetResponse()
                    else:
                        fetch_logger.error(
                            f"S3 error checking for dataset file: {e}", exc_info=True
                        )
                        raise

                url = await s3_client.generate_presigned_url(
                    "get_object",
                    Params={"Bucket": self.output_bucket, "Key": output_key},
                    ExpiresIn=3600,
                )
                urls.append(PresignedUrl(url=url))
                fetch_logger.info(f"Generated presigned URL for {output_key}")
        except Exception as e:
            fetch_logger.error(
                f"Failed to generate presigned URL for fetching dataset: {e}",
                exc_info=True,
            )
            await context.abort(
                grpc.StatusCode.INTERNAL,
                f"Could not generate URL for dataset ref '{request.ref}'. Error: {str(e)}",
            )
            return OfflineFeaturesServiceFetchDatasetResponse()

        return OfflineFeaturesServiceFetchDatasetResponse(
            ref=request.ref, presigned_urls=urls
        )

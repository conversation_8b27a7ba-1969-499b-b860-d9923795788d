import asyncio
import math
from typing import Any, TYPE_CHECKING
import datetime as dt_module
from datetime import datetime, timezone, timedelta
import polars as pl

from mlutils.asyncio import gather_dict
from temporalio import activity
from temporalio.client import (
    Schedule,
    ScheduleActionStartWorkflow,
    ScheduleSpec,
    ScheduleIntervalSpec,
    ScheduleState,
    SchedulePolicy,
    ScheduleOverlapPolicy,
)

from src.queries import QUERIES
from src.shared import (
    FamilyDetails,
    UpdateFamilyStatusDetails,
    FamilyBackfillDetails,
    TASK_QUEUE_NAME,
)
from src.families import Family, Registry
from src.sources.batch.base import BatchSource
from src.stores import OfflineStore, OnlineStore

if TYPE_CHECKING:
    from src.persistence_activities import FamilyPersistenceActivities


LOOKBACK_DAYS = 7
MAX_PARTITIONS = 16
AVG_ROWS_PER_SECOND = 500
MIN_MAX_WINDOW_SECONDS = 60
MAX_MAX_WINDOW_SECONDS = 60 * 60 * 24 * 366
AVAILABLE_BYTES = 500 * 2**30
MAX_FAMILIES = 200
MAX_RETAINED_BYTES_PER_FAMILY = AVAILABLE_BYTES / MAX_FAMILIES
AVG_EVENT_SIZE_BYTES = 100
MAX_RETAINED_EVENTS_PER_FAMILY = MAX_RETAINED_BYTES_PER_FAMILY / AVG_EVENT_SIZE_BYTES
BACKFILL_BATCH_SIZE = 50000

activity_logger = activity.logger


def ensure_utc(dt_val: Any) -> datetime | None:
    """Converts various timestamp inputs to timezone-aware UTC datetime."""
    if isinstance(dt_val, datetime):
        if dt_val.tzinfo is None:
            return dt_val.replace(tzinfo=timezone.utc)
        return dt_val.astimezone(timezone.utc)
    elif isinstance(dt_val, str):
        try:
            if dt_val.endswith("Z"):
                dt_val = dt_val[:-1] + "+00:00"
            dt = dt_module.datetime.fromisoformat(dt_val)
            if dt.tzinfo is None:
                return dt.replace(tzinfo=timezone.utc)
            return dt.astimezone(timezone.utc)
        except ValueError:
            activity_logger.warning(f"Could not parse timestamp string: {dt_val}")
            return None
    elif hasattr(dt_val, "to_pydatetime"):
        try:
            py_dt = dt_val.to_pydatetime()
            if py_dt is None or py_dt != py_dt:
                return None
            if py_dt.tzinfo is None:
                return py_dt.replace(tzinfo=timezone.utc)
            return py_dt.astimezone(timezone.utc)
        except Exception as e:
            activity_logger.warning(
                f"Could not convert pandas-like timestamp: {dt_val}. Error: {e}"
            )
            return None
    elif dt_val is None:
        return None

    activity_logger.warning(
        f"Cannot convert timestamp input of type {type(dt_val)}: {dt_val}"
    )
    return None


async def determine_num_partitions(family: Family, batch_source: BatchSource) -> int:
    query = QUERIES["plan_num_partitions"].render(
        family=family, lookback_days=LOOKBACK_DAYS
    )
    activity_logger.debug(
        f"Executing query for determine_num_partitions: {query[:500]}..."
    )
    result_df = await batch_source.fetch_all(query)
    activity_logger.debug(
        f"Received result type {type(result_df)} for determine_num_partitions"
    )

    if not isinstance(result_df, pl.DataFrame) or result_df.is_empty():
        activity_logger.warning(
            f"No data found or unexpected result type ({type(result_df)}) for planning partitions for family {family.name}. Defaulting to 1 partition."
        )
        return 1

    if "event_count" not in result_df.columns:
        activity_logger.error(
            f"'event_count' column missing in partition planning query result for family {family.name}. Defaulting to 1 partition."
        )
        return 1

    try:
        p95_event_count_per_hour = result_df.select(
            pl.col("event_count").cast(pl.Float64, strict=False)
        )["event_count"].quantile(0.95, interpolation="linear")
    except Exception as e:
        activity_logger.error(
            f"Error calculating quantile for {family.name}: {e}. Defaulting to 1 partition."
        )
        return 1

    if p95_event_count_per_hour is None or math.isnan(p95_event_count_per_hour):
        activity_logger.warning(
            f"P95 event count is None or NaN for family {family.name}. Defaulting to 1 partition."
        )
        return 1

    p95_event_count_per_second = p95_event_count_per_hour / (60 * 60)
    num_partitions_calculated = min(
        math.ceil(p95_event_count_per_second / AVG_ROWS_PER_SECOND), MAX_PARTITIONS
    )
    num_partitions = max(1, int(num_partitions_calculated))
    activity_logger.info(f"Determined {num_partitions} partitions for {family.name}")
    return num_partitions


async def determine_max_window_seconds(
    family: Family, batch_source: BatchSource
) -> int:
    query = QUERIES["plan_max_window_seconds"].render(
        family=family, lookback_days=LOOKBACK_DAYS
    )
    activity_logger.debug(
        f"Executing query for determine_max_window_seconds: {query[:500]}..."
    )
    result_df = await batch_source.fetch_all(query)
    activity_logger.debug(
        f"Received result type {type(result_df)} for determine_max_window_seconds"
    )

    if not isinstance(result_df, pl.DataFrame) or result_df.is_empty():
        activity_logger.warning(
            f"No data found or unexpected result type ({type(result_df)}) for planning max window seconds for family {family.name}. Using default min window."
        )
        return MIN_MAX_WINDOW_SECONDS

    if "event_count" not in result_df.columns:
        activity_logger.error(
            f"'event_count' column missing in max window planning query result for family {family.name}. Using default min window."
        )
        return MIN_MAX_WINDOW_SECONDS

    try:
        total_event_count = result_df["event_count"].item(0)
    except (IndexError, KeyError, TypeError) as e:
        activity_logger.error(
            f"Error accessing event_count for {family.name}: {e}. Using default min window."
        )
        return MIN_MAX_WINDOW_SECONDS

    if (
        not isinstance(total_event_count, (int, float))
        or total_event_count is None
        or (isinstance(total_event_count, float) and math.isnan(total_event_count))
    ):
        activity_logger.warning(
            f"Unexpected type or value for total_event_count: {total_event_count} ({type(total_event_count)}). Using default min window."
        )
        return MIN_MAX_WINDOW_SECONDS

    events_per_second = total_event_count / (LOOKBACK_DAYS * 24 * 60 * 60)
    if events_per_second <= 0:
        activity_logger.warning(
            f"Non-positive events per second ({events_per_second}) calculated for family {family.name}. Using max window."
        )
        return MAX_MAX_WINDOW_SECONDS

    max_window_seconds_calculated = math.ceil(
        MAX_RETAINED_EVENTS_PER_FAMILY / events_per_second
    )
    max_window_seconds = max(
        min(int(max_window_seconds_calculated), MAX_MAX_WINDOW_SECONDS),
        MIN_MAX_WINDOW_SECONDS,
    )
    activity_logger.info(
        f"Determined max_window_seconds={max_window_seconds} for {family.name}"
    )
    return max_window_seconds


async def determine_increment_interval_seconds(
    family: Family, batch_source: BatchSource
) -> int:
    # TODO: Make this configurable or derive it
    increment_interval_seconds = 60 * 60 * 4
    activity.logger.info(
        f"Determined increment_interval_seconds={increment_interval_seconds} for {family.name}"
    )
    return increment_interval_seconds


PIPELINE_SETTINGS = {
    "num_partitions": determine_num_partitions,
    "max_window_seconds": determine_max_window_seconds,
    "increment_interval_seconds": determine_increment_interval_seconds,
}


class FamilyPipelineActivities:
    def __init__(
        self, registry: Registry, batch_source: BatchSource, temporal_wrapper=None
    ) -> None:
        self.registry = registry
        self.batch_source = batch_source
        self.temporal_wrapper = temporal_wrapper

    @activity.defn
    async def plan(self, family_details: FamilyDetails) -> dict:
        family = await self.registry.fetch_one(family_details.name)
        activity_logger.info(f"Starting plan activity for {family_details.name}")
        try:
            settings = await gather_dict(
                {
                    key: fn(family, self.batch_source)
                    for key, fn in PIPELINE_SETTINGS.items()
                }
            )
            activity_logger.info(
                f"Gathered pipeline settings for {family_details.name}: {settings}"
            )
        except Exception as e:
            activity_logger.error(
                f"Error determining pipeline settings for {family_details.name}: {e}",
                exc_info=True,
            )
            raise RuntimeError(f"Failed to plan pipeline settings: {e}") from e

        required_keys = [
            "num_partitions",
            "max_window_seconds",
            "increment_interval_seconds",
        ]
        if not all(key in settings for key in required_keys):
            missing_keys = [key for key in required_keys if key not in settings]
            activity_logger.error(
                f"Missing required pipeline settings for {family_details.name}: {missing_keys}"
            )
            raise ValueError(f"Missing pipeline settings: {missing_keys}")

        if not all(
            isinstance(settings[key], (int, float)) and settings[key] >= 0
            for key in required_keys
        ):
            invalid_settings = {
                k: v
                for k, v in settings.items()
                if not (isinstance(v, (int, float)) and v >= 0)
            }
            activity_logger.error(
                f"Invalid pipeline settings calculated for {family_details.name}: {invalid_settings}"
            )
            raise ValueError(f"Invalid pipeline settings: {invalid_settings}")

        if (
            not isinstance(settings["num_partitions"], int)
            or settings["num_partitions"] < 1
        ):
            activity_logger.error(
                f"Invalid num_partitions calculated for {family_details.name}: {settings['num_partitions']}"
            )
            raise ValueError(f"Invalid num_partitions: {settings['num_partitions']}")

        await self.registry.set_pipeline_settings(family_details.name, settings)
        activity_logger.info(
            f"Planned and saved pipeline settings for {family_details.name}: {settings}"
        )
        return {
            "num_partitions": settings["num_partitions"],
            "increment_interval_seconds": settings["increment_interval_seconds"],
        }

    @activity.defn
    async def update_status(self, details: UpdateFamilyStatusDetails) -> None:
        activity_logger.info(
            f"Updating status for {details.name}: {details.status.name} ({details.status_detail})"
        )
        await self.registry.update_status(
            details.name, details.status, details.status_detail
        )

    @activity.defn
    async def create_or_update_schedule(
        self,
        family_details: FamilyDetails,
        interval_seconds: int,
        workflow_type_name: str,
    ) -> None:
        if self.temporal_wrapper is None:
            raise RuntimeError("Temporal client wrapper is not initialized")

        schedule_id = f"schedule-family-incremental-{family_details.name}"
        action = ScheduleActionStartWorkflow(
            workflow=workflow_type_name,
            args=[family_details],
            id=f"family-incremental-run-{family_details.name}-{{timestamp}}",
            task_queue=TASK_QUEUE_NAME,
        )
        schedule = Schedule(
            action=action,
            spec=ScheduleSpec(
                intervals=[
                    ScheduleIntervalSpec(every=timedelta(seconds=interval_seconds))
                ],
            ),
            state=ScheduleState(
                paused=False,
            ),
            policy=SchedulePolicy(
                overlap=ScheduleOverlapPolicy.SKIP,
            ),
        )

        activity_logger.info(
            f"Creating/updating schedule '{schedule_id}' for family '{family_details.name}' "
            f"with interval {interval_seconds}s to run workflow '{workflow_type_name}'."
        )
        try:
            await self.temporal_wrapper.client.create_schedule(
                schedule_id,
                schedule,
                id_conflict_policy="UPDATE",
            )
            activity_logger.info(
                f"Successfully created/updated schedule '{schedule_id}'."
            )
        except Exception as e:
            if "already exists" in str(e).lower():
                activity_logger.info(
                    f"Schedule '{schedule_id}' already exists. Attempting update."
                )
                try:
                    handle = self.temporal_wrapper.client.get_schedule_handle(
                        schedule_id
                    )
                    await handle.update(
                        lambda prev_schedule_update: prev_schedule_update.apply_patch(
                            schedule
                        )
                    )
                    activity_logger.info(
                        f"Successfully updated schedule '{schedule_id}'."
                    )
                except Exception as update_e:
                    activity_logger.error(
                        f"Failed to update schedule '{schedule_id}': {update_e}",
                        exc_info=True,
                    )
                    raise
            else:
                activity_logger.error(
                    f"Failed to create schedule '{schedule_id}': {e}", exc_info=True
                )
                raise

    @activity.defn
    async def delete_schedule(self, family_details: FamilyDetails) -> None:
        if self.temporal_wrapper is None:
            raise RuntimeError("Temporal client wrapper is not initialized")

        schedule_id = f"schedule-family-incremental-{family_details.name}"
        activity_logger.info(
            f"Deleting schedule '{schedule_id}' for family '{family_details.name}'."
        )
        try:
            handle = self.temporal_wrapper.client.get_schedule_handle(schedule_id)
            await handle.delete()
            activity_logger.info(f"Successfully deleted schedule '{schedule_id}'.")
        except Exception as e:
            activity_logger.error(
                f"Failed to delete schedule '{schedule_id}': {e}", exc_info=True
            )
            raise

    @activity.defn
    async def get_family_config(self, family_details: FamilyDetails) -> dict:
        """
        Retrieves the family configuration, including late_arriving_data_lag_seconds.
        """
        activity_logger.info(f"Fetching family configuration for {family_details.name}")
        try:
            family = await self.registry.fetch_one(family_details.name)

            late_arriving_data_lag_seconds = (
                family.config.source.batch.late_arriving_data_lag_seconds
            )

            config_dict = {
                "late_arriving_data_lag_seconds": late_arriving_data_lag_seconds
            }

            activity_logger.info(
                f"Retrieved family configuration for {family_details.name}: {config_dict}"
            )
            return config_dict
        except Exception as e:
            activity_logger.error(
                f"Error fetching family configuration for {family_details.name}: {e}",
                exc_info=True,
            )
            raise


class FamilyBackfillActivities:
    def __init__(
        self,
        registry: Registry,
        batch_source: BatchSource,
        offline_store: OfflineStore,
        online_store: OnlineStore,
        family_persistence_activities: "FamilyPersistenceActivities",
    ) -> None:
        self.registry = registry
        self.batch_source = batch_source
        self.offline_store = offline_store
        self.online_store = online_store
        self.family_persistence_activities = family_persistence_activities

    @activity.defn(name="backfill")
    async def backfill(self, family_backfill_details: FamilyBackfillDetails) -> None:
        family_name = family_backfill_details.name
        partition = family_backfill_details.partition

        activity_logger.info(
            f"Starting backfill activity for {family_name}, partition {partition}"
        )
        try:
            family: Family = await self.registry.fetch_one(family_name)
            query = QUERIES["backfill_fetch"].render(family=family, partition=partition)
            activity_logger.debug(
                f"Executing backfill query for {family_name}/{partition}: {query[:500]}..."
            )

            entity_states: dict[str, dict[str, Any]] = {}
            processed_row_count_total = 0
            ts_col = family.config.timestamp_column
            id_col = family.config.id_column
            identifier_cols = family.config.identifier_columns

            async for df_batch in self.batch_source.fetch_batches(
                query, BACKFILL_BATCH_SIZE
            ):
                if not isinstance(df_batch, pl.DataFrame):
                    activity_logger.warning(
                        f"Received non-DataFrame batch (type: {type(df_batch)}). Skipping."
                    )
                    continue

                if df_batch.is_empty():
                    activity_logger.debug(
                        "Batch DataFrame is empty, skipping processing."
                    )
                    continue

                num_rows_in_batch = len(df_batch)
                activity_logger.info(
                    f"Processing batch DataFrame with {num_rows_in_batch} rows for {family_name}/{partition}"
                )

                try:
                    if ts_col not in df_batch.columns:
                        activity_logger.error(
                            f"Timestamp column '{ts_col}' not found in batch. Skipping batch."
                        )
                        continue

                    df_batch = df_batch.with_columns(
                        pl.col(ts_col)
                        .map_elements(
                            ensure_utc,
                            return_dtype=pl.Datetime(time_unit="us", time_zone="UTC"),
                            skip_nulls=False,
                        )
                        .alias(ts_col)
                    )
                    initial_rows = len(df_batch)
                    df_batch = df_batch.filter(
                        pl.col(ts_col).is_not_null()
                    )  # TODO: explicitly set timestamp in backfill query or raise error
                    if len(df_batch) < initial_rows:
                        activity_logger.warning(
                            f"Filtered out {initial_rows - len(df_batch)} rows due to invalid/null timestamps."
                        )
                    if df_batch.is_empty():
                        activity_logger.warning(
                            "Batch is empty after timestamp filtering."
                        )
                        continue

                    # TODO: consider moving to SQL query
                    sort_columns = identifier_cols + [ts_col, id_col]
                    missing_sort_cols = [
                        c for c in sort_columns if c not in df_batch.columns
                    ]
                    if missing_sort_cols:
                        activity_logger.error(
                            f"Missing columns required for sorting: {missing_sort_cols}. Cannot process batch."
                        )
                        continue

                    df_batch = df_batch.sort(sort_columns)

                    processed_rows_list = []
                    for group_keys, group_df in df_batch.group_by(
                        identifier_cols, maintain_order=True
                    ):
                        if isinstance(group_keys, tuple):
                            group_key_dict = dict(zip(identifier_cols, group_keys))
                        else:
                            group_key_dict = {identifier_cols[0]: group_keys}

                        try:
                            packed_identifiers = family.pack_identifiers(group_key_dict)
                        except KeyError as e:
                            activity_logger.warning(
                                f"Missing identifier {e} in group key {group_key_dict}. Skipping group."
                            )
                            continue

                        previous_entity_state = entity_states.get(
                            packed_identifiers, {}
                        )
                        current_entity_state = previous_entity_state.copy()

                        for row_struct in group_df.iter_rows(named=True):
                            for descriptor in family.aggregation_descriptors:
                                value_for_agg = row_struct.get(descriptor.column)
                                aggregation_impl = descriptor.aggregation
                                state_key = descriptor.state_key

                                previous_agg_state = current_entity_state.get(state_key)

                                try:
                                    new_agg_state = aggregation_impl.accumulate(
                                        value_for_agg, previous_agg_state
                                    )

                                    row_struct[state_key] = new_agg_state
                                    current_entity_state[state_key] = new_agg_state
                                except Exception as agg_err:
                                    activity_logger.error(
                                        f"Error during aggregation {descriptor.state_key} for row {row_struct.get(id_col)}: {agg_err}",
                                        exc_info=True,
                                    )
                                    row_struct[state_key] = previous_agg_state

                            processed_rows_list.append(row_struct)

                        entity_states[packed_identifiers] = current_entity_state

                    if not processed_rows_list:
                        activity_logger.warning(
                            "No rows were processed in this batch after grouping/accumulation."
                        )
                        continue

                    processed_df = pl.DataFrame(processed_rows_list, strict=False)
                    processed_row_count_batch = len(processed_df)
                    processed_row_count_total += processed_row_count_batch
                    activity_logger.info(
                        f"Finished state calculation. Processed rows this batch: {processed_row_count_batch}"
                    )

                    # TODO: move all checks to beginning, fail fast
                    if ts_col in processed_df.columns:
                        processed_df = processed_df.with_columns(
                            pl.col(ts_col).cast(
                                pl.Datetime(time_unit="us", time_zone="UTC")
                            )
                        )
                    else:
                        activity_logger.error(
                            f"Timestamp column '{ts_col}' missing from processed_df. Cannot write batch."
                        )
                        continue

                    activity_logger.debug(
                        "Grouping processed DataFrame by date for S3 write..."
                    )
                    grouped_by_date = processed_df.group_by(
                        pl.col(ts_col).dt.date().alias("event_date")
                    )
                    unique_dates = set()
                    for date_group_key, _ in grouped_by_date:
                        event_date = (
                            date_group_key[0]
                            if isinstance(date_group_key, tuple)
                            else date_group_key
                        )
                        unique_dates.add(event_date)

                    dates_in_batch = len(unique_dates)
                    activity_logger.info(
                        f"Grouped {processed_row_count_batch} rows into {dates_in_batch} dates for S3 write."
                    )

                    if not grouped_by_date.agg([]).is_empty():
                        s3_write_tasks = []
                        for date_group_key, date_df_group in grouped_by_date:
                            event_date = (
                                date_group_key[0]
                                if isinstance(date_group_key, tuple)
                                else date_group_key
                            )

                            if date_df_group.is_empty():
                                activity_logger.warning(
                                    f"DataFrame for S3 write on date {event_date} is empty. Skipping write."
                                )
                                continue

                            activity_logger.debug(
                                f"Scheduling S3 write for date {event_date}, partition {partition} with {len(date_df_group)} rows."
                            )
                            s3_write_tasks.append(
                                self.offline_store.write_batch(
                                    family=family,
                                    df=date_df_group,
                                    partition=partition,
                                    date=event_date,
                                )
                            )

                        if s3_write_tasks:
                            await asyncio.gather(*s3_write_tasks)
                            processed_dates_str = ", ".join(
                                sorted(
                                    [
                                        str(d[0] if isinstance(d, tuple) else d)
                                        for d, _ in grouped_by_date
                                    ]
                                )
                            )
                            activity_logger.debug(
                                f"Successfully wrote S3 data for dates: {processed_dates_str}"
                            )
                        else:
                            activity_logger.info(
                                "No S3 write tasks were scheduled for this batch."
                            )
                    else:
                        activity_logger.info(
                            "No rows grouped by date to write to S3 for this batch."
                        )

                    activity_logger.debug(
                        f"Writing {processed_row_count_batch} processed rows to Redis..."
                    )
                    try:
                        rows_for_online_store = processed_df.to_dicts()

                        identifiers_to_flush = await self.online_store.write_rows(
                            family, rows_for_online_store
                        )
                        activity_logger.debug(
                            f"Finished writing to Redis for batch. Potential flushes needed for: {identifiers_to_flush}"
                        )

                        if identifiers_to_flush:
                            flush_tasks = []
                            for packed_id in identifiers_to_flush:
                                activity_logger.info(
                                    f"Triggering flush activity for entity: {packed_id}"
                                )
                                from src.shared import FlushDetails

                                flush_details = FlushDetails(
                                    family_name=family.name,
                                    packed_identifiers=packed_id,
                                )
                                flush_tasks.append(
                                    self.family_persistence_activities.flush_entity_redis_to_s3(
                                        flush_details
                                    )
                                )
                            if flush_tasks:
                                try:
                                    await asyncio.gather(*flush_tasks)
                                    activity_logger.info(
                                        f"Completed flush tasks for {len(flush_tasks)} entities."
                                    )
                                except Exception as flush_err:
                                    activity_logger.error(
                                        f"Error during flush activity calls: {flush_err}",
                                        exc_info=True,
                                    )

                    except Exception as online_store_err:
                        activity_logger.error(
                            f"Error writing to Online Store or triggering flush: {online_store_err}",
                            exc_info=True,
                        )

                except Exception as e:
                    activity_logger.error(
                        f"Error processing or writing batch DataFrame: {e}",
                        exc_info=True,
                    )
                    raise

                activity_logger.debug(
                    f"Heartbeating after processing batch. Total rows processed so far: {processed_row_count_total}"
                )
                activity.heartbeat()

            activity_logger.info(
                f"Backfill loop finished for {family_name}, partition {partition}. Total processed: {processed_row_count_total}"
            )

        except Exception as e:
            activity_logger.error(
                f"Backfill failed for {family_name}, partition {partition}: {e}",
                exc_info=True,
            )
            raise

        activity_logger.info(
            f"Backfill successfully completed activity logic for {family_name}, partition {partition}"
        )


class FamilyIncrementalActivities:
    def __init__(
        self,
        registry: Registry,
        batch_source: BatchSource,
        online_store: OnlineStore,
        offline_store: OfflineStore,
        persistence_activities: "FamilyPersistenceActivities",
    ):
        self.registry = registry
        self.batch_source = batch_source
        self.online_store = online_store
        self.offline_store = offline_store
        self.persistence_activities = persistence_activities

    @activity.defn
    async def get_frontier(self, family_details: FamilyDetails) -> str:
        """
        Fetches the current frontier timestamp for the family as ISO string.
        """
        activity_logger.info(f"Fetching current frontier for {family_details.name}")
        try:
            family = await self.registry.fetch_one(family_details.name)
            frontier_iso = family.frontier.isoformat()
            activity_logger.info(
                f"Current frontier for {family_details.name} is {frontier_iso}"
            )
            return frontier_iso
        except Exception as e:
            activity_logger.error(
                f"Error fetching frontier for {family_details.name}: {e}", exc_info=True
            )
            raise

    @activity.defn
    async def set_frontier(
        self, family_details: FamilyDetails, new_frontier_iso: str
    ) -> None:
        """Updates the frontier timestamp for the family from ISO string."""
        activity_logger.info(
            f"Setting new frontier for {family_details.name} to {new_frontier_iso}"
        )
        try:
            new_frontier = datetime.fromisoformat(new_frontier_iso)
            if new_frontier.tzinfo is None:
                new_frontier = new_frontier.replace(tzinfo=timezone.utc)
            else:
                new_frontier = new_frontier.astimezone(timezone.utc)

            await self.registry.update_frontier(family_details.name, new_frontier)
            activity_logger.info(
                f"Successfully set new frontier for {family_details.name}"
            )
        except Exception as e:
            activity_logger.error(
                f"Error setting frontier for {family_details.name}: {e}", exc_info=True
            )
            raise

    @activity.defn
    async def process_incremental_batch(
        self, family_details: FamilyDetails, current_frontier_iso: str
    ) -> str:
        """
        Processes a batch of incremental data since the last frontier.
        """
        family_name = family_details.name
        try:
            current_frontier = datetime.fromisoformat(current_frontier_iso)
            if current_frontier.tzinfo is None:
                current_frontier = current_frontier.replace(tzinfo=timezone.utc)
            else:
                current_frontier = current_frontier.astimezone(timezone.utc)
        except ValueError:
            activity_logger.error(
                f"Invalid ISO format for current_frontier: {current_frontier_iso}"
            )
            raise ValueError(
                f"Invalid ISO format for current_frontier: {current_frontier_iso}"
            )

        activity_logger.info(
            f"Starting incremental batch processing for {family_name} from {current_frontier_iso}"
        )

        latest_processed_ts = current_frontier
        processed_row_count_total = 0

        try:
            family: Family = await self.registry.fetch_one(family_name)
            query = QUERIES["incremental_fetch"].render(
                family=family, current_frontier=current_frontier
            )
            activity_logger.debug(
                f"Executing incremental fetch query: {query[:500]}..."
            )

            entity_last_known_state_rows: dict[str, dict | None] = {}

            ts_col = family.config.timestamp_column
            id_col = family.config.id_column
            identifier_cols = family.config.identifier_columns

            async for df_batch in self.batch_source.fetch_batches(
                query, BACKFILL_BATCH_SIZE
            ):
                if not isinstance(df_batch, pl.DataFrame):
                    activity_logger.warning(
                        f"Received non-DataFrame batch (type: {type(df_batch)}). Skipping."
                    )
                    continue

                # if df_batch.is_empty():
                #     activity_logger.debug(
                #         "Batch DataFrame is empty, skipping processing."
                #     )
                #     continue

                num_rows_in_batch = len(df_batch)
                activity_logger.info(
                    f"Processing incremental batch DataFrame with {num_rows_in_batch} rows for {family_name}"
                )

                try:
                    if ts_col not in df_batch.columns:
                        activity_logger.error(
                            f"Timestamp column '{ts_col}' not found in incremental batch. Skipping batch."
                        )
                        continue
                    df_batch = df_batch.with_columns(
                        pl.col(ts_col)
                        .map_elements(
                            ensure_utc,
                            return_dtype=pl.Datetime(time_unit="us", time_zone="UTC"),
                            skip_nulls=False,
                        )
                        .alias(ts_col)
                    )
                    initial_rows = len(df_batch)
                    df_batch = df_batch.filter(pl.col(ts_col).is_not_null())
                    if len(df_batch) < initial_rows:
                        activity_logger.warning(
                            f"Filtered out {initial_rows - len(df_batch)} rows due to invalid/null timestamps."
                        )
                    if df_batch.is_empty():
                        activity_logger.warning(
                            "Batch is empty after timestamp filtering."
                        )
                        continue

                    sort_columns = identifier_cols + [ts_col, id_col]
                    missing_sort_cols = [
                        c for c in sort_columns if c not in df_batch.columns
                    ]
                    if missing_sort_cols:
                        activity_logger.error(
                            f"Missing columns required for sorting: {missing_sort_cols}. Cannot process batch."
                        )
                        continue
                    df_batch = df_batch.sort(sort_columns)

                    processed_rows_list = []
                    entities_in_batch = set()

                    if identifier_cols:
                        unique_entities_df = df_batch.select(identifier_cols).unique()
                        for entity_row in unique_entities_df.iter_rows(named=True):
                            try:
                                packed_id = family.pack_identifiers(entity_row)
                                entities_in_batch.add(packed_id)
                            except KeyError as e:
                                activity_logger.warning(
                                    f"Cannot pack identifiers for row {entity_row}: {e}"
                                )
                                continue
                    else:
                        activity_logger.warning(
                            "No identifier columns defined, cannot process entities."
                        )
                        continue

                    fetch_tasks = {}
                    for packed_id in entities_in_batch:
                        if packed_id not in entity_last_known_state_rows:
                            fetch_tasks[packed_id] = self.online_store.read_recent_rows(
                                family, packed_id, num_rows=1
                            )

                    if fetch_tasks:
                        fetched_states = await gather_dict(fetch_tasks)
                        for packed_id, state_rows in fetched_states.items():
                            entity_last_known_state_rows[packed_id] = (
                                state_rows[0] if state_rows else None
                            )
                            if state_rows:
                                activity_logger.debug(
                                    f"Fetched last state for entity {packed_id}"
                                )
                            else:
                                activity_logger.debug(
                                    f"No previous state found for new entity {packed_id}"
                                )

                    for group_keys, group_df in df_batch.group_by(
                        identifier_cols, maintain_order=True
                    ):
                        if isinstance(group_keys, tuple):
                            group_key_dict = dict(zip(identifier_cols, group_keys))
                        else:
                            group_key_dict = {identifier_cols[0]: group_keys}

                        try:
                            packed_identifiers = family.pack_identifiers(group_key_dict)
                        except KeyError as e:
                            activity_logger.warning(
                                f"Missing identifier {e} in group key {group_key_dict}. Skipping group."
                            )
                            continue

                        last_known_row = entity_last_known_state_rows.get(
                            packed_identifiers
                        )
                        current_entity_state = {}
                        if last_known_row:
                            for descriptor in family.aggregation_descriptors:
                                state_key = descriptor.state_key
                                if state_key in last_known_row:
                                    current_entity_state[state_key] = last_known_row[
                                        state_key
                                    ]

                        for row_struct in group_df.iter_rows(named=True):
                            for descriptor in family.aggregation_descriptors:
                                value_for_agg = row_struct.get(descriptor.column)
                                aggregation_impl = descriptor.aggregation
                                state_key = descriptor.state_key
                                previous_agg_state = current_entity_state.get(state_key)

                                try:
                                    new_agg_state = aggregation_impl.accumulate(
                                        value_for_agg, previous_agg_state
                                    )
                                    row_struct[state_key] = new_agg_state
                                    current_entity_state[state_key] = new_agg_state
                                except Exception as agg_err:
                                    activity_logger.error(
                                        f"Error during aggregation {descriptor.state_key} for row {row_struct.get(id_col)}: {agg_err}",
                                        exc_info=True,
                                    )
                                    row_struct[state_key] = previous_agg_state

                            processed_rows_list.append(row_struct)

                            row_ts = row_struct.get(ts_col)
                            if row_ts and row_ts > latest_processed_ts:
                                latest_processed_ts = row_ts

                        final_state_row = {
                            ts_col: latest_processed_ts,
                            id_col: row_struct.get(id_col),
                        }
                        final_state_row.update(group_key_dict)
                        final_state_row.update(current_entity_state)
                        entity_last_known_state_rows[packed_identifiers] = (
                            final_state_row
                        )

                    if not processed_rows_list:
                        activity_logger.warning(
                            "No rows were processed in this incremental batch after grouping/accumulation."
                        )
                        continue

                    processed_df = pl.DataFrame(processed_rows_list, strict=False)
                    processed_row_count_batch = len(processed_df)
                    processed_row_count_total += processed_row_count_batch
                    activity_logger.info(
                        f"Finished state calculation. Processed rows this batch: {processed_row_count_batch}"
                    )

                    if ts_col in processed_df.columns:
                        processed_df = processed_df.with_columns(
                            pl.col(ts_col).cast(
                                pl.Datetime(time_unit="us", time_zone="UTC")
                            )
                        )
                    else:
                        activity_logger.error(
                            f"Timestamp column '{ts_col}' missing from processed_df. Cannot write batch."
                        )
                        continue

                    grouped_by_date = processed_df.group_by(
                        pl.col(ts_col).dt.date().alias("event_date")
                    )
                    dates_in_batch = grouped_by_date.len()
                    activity_logger.info(
                        f"Grouped {processed_row_count_batch} rows into {dates_in_batch} dates for S3 write."
                    )

                    if not grouped_by_date.agg([]).is_empty():
                        s3_write_tasks = []
                        for date_group_key, date_df_group in grouped_by_date:
                            event_date = (
                                date_group_key[0]
                                if isinstance(date_group_key, tuple)
                                else date_group_key
                            )

                            if date_df_group.is_empty():
                                activity_logger.warning(
                                    f"DataFrame for S3 write on date {event_date} is empty. Skipping write."
                                )
                                continue

                            first_row_dict = date_df_group.row(0, named=True)
                            try:
                                partition_packed_id = family.pack_identifiers(
                                    first_row_dict
                                )
                                partition_num = (
                                    family.calculate_partition(
                                        partition_packed_id,
                                        family.pipeline_settings.num_partitions,
                                    )
                                    if family.pipeline_settings
                                    else 0
                                )
                            except KeyError:
                                activity_logger.warning(
                                    f"Could not pack identifiers for first row on date {event_date}, defaulting partition to 0"
                                )
                                partition_num = 0
                            except AttributeError:
                                activity_logger.warning(
                                    "Family or pipeline_settings missing for partition calculation, defaulting to 0"
                                )
                                partition_num = 0

                            activity_logger.debug(
                                f"Scheduling S3 write for date {event_date}, partition {partition_num} with {len(date_df_group)} rows."
                            )
                            s3_write_tasks.append(
                                self.offline_store.write_batch(
                                    family=family,
                                    df=date_df_group,
                                    partition=partition_num,
                                    date=event_date,
                                )
                            )
                        if s3_write_tasks:
                            await asyncio.gather(*s3_write_tasks)
                            activity_logger.debug(
                                "Finished S3 writes for incremental batch."
                            )
                        else:
                            activity_logger.info(
                                "No S3 write tasks scheduled for this incremental batch."
                            )
                    else:
                        activity_logger.info(
                            "No rows grouped by date to write to S3 for this incremental batch."
                        )

                    activity_logger.debug(
                        f"Writing {processed_row_count_batch} processed rows to Redis..."
                    )
                    try:
                        rows_for_online_store = processed_df.to_dicts()
                        identifiers_to_flush = await self.online_store.write_rows(
                            family, rows_for_online_store
                        )
                        activity_logger.debug(
                            f"Finished writing to Redis. Potential flushes needed for: {identifiers_to_flush}"
                        )

                        if identifiers_to_flush:
                            flush_tasks = []
                            for packed_id in identifiers_to_flush:
                                activity_logger.info(
                                    f"Triggering flush activity for entity: {packed_id}"
                                )
                                from src.shared import FlushDetails

                                flush_details = FlushDetails(
                                    family_name=family.name,
                                    packed_identifiers=packed_id,
                                )
                                flush_tasks.append(
                                    self.persistence_activities.flush_entity_redis_to_s3(
                                        flush_details
                                    )
                                )
                            if flush_tasks:
                                try:
                                    await asyncio.gather(*flush_tasks)
                                    activity_logger.info(
                                        f"Completed flush tasks for {len(flush_tasks)} entities."
                                    )
                                except Exception as flush_err:
                                    activity_logger.error(
                                        f"Error during flush activity calls: {flush_err}",
                                        exc_info=True,
                                    )

                    except Exception as online_store_err:
                        activity_logger.error(
                            f"Error writing to Online Store or triggering flush: {online_store_err}",
                            exc_info=True,
                        )
                        raise

                except Exception as e:
                    activity_logger.error(
                        f"Error processing or writing incremental batch DataFrame: {e}",
                        exc_info=True,
                    )
                    raise

                activity_logger.debug(
                    f"Heartbeating after processing batch. Total rows processed so far: {processed_row_count_total}"
                )
                activity.heartbeat()

            activity_logger.info(
                f"Incremental batch loop finished. Total processed: {processed_row_count_total}. Latest TS: {latest_processed_ts.isoformat()}"
            )

        except Exception as e:
            activity_logger.error(f"Incremental batch failed: {e}", exc_info=True)
            raise

        return latest_processed_ts.isoformat()
